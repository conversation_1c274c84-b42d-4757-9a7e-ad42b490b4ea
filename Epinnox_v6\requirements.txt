# Epinnox v6 - AI-Powered Trading System Dependencies
# Updated for Phase 3 optimization with complete dependency coverage

# Core data processing
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Technical analysis
pandas-ta>=0.3.14b
TA-Lib>=0.4.25
ta>=0.10.2

# Machine Learning
scikit-learn>=1.3.0
joblib>=1.3.0

# Exchange connectivity
ccxt>=4.0.0
requests>=2.28.0
websockets>=11.0.0
websocket-client>=1.6.0

# GUI Framework
PyQt5>=5.15.0
pyqtgraph>=0.13.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0

# AI/ML Models
torch>=2.0.0
transformers>=4.30.0
openai>=1.0.0

# Configuration and utilities
PyYAML>=6.0.0
python-dateutil>=2.8.0
colorama>=0.4.6

# System monitoring
psutil>=5.9.0

# Async support
aiohttp>=3.8.0
asyncio-mqtt>=0.13.0

# Development and testing
pytest>=7.4.0
pytest-qt>=4.2.0
pytest-asyncio>=0.21.0
flake8>=6.0.0
black>=23.0.0

# Optional: GPU acceleration (uncomment if GPU available)
# torch-audio>=2.0.0
# torch-vision>=0.15.0

# Optional: Enhanced performance (uncomment for production)
# uvloop>=0.19.0; sys_platform != "win32"
# cython>=3.0.0

# Security updates
cryptography>=41.0.0
urllib3>=2.0.0
