# Epinnox v6 - AI-Powered Trading System Dependencies

# Core data processing
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Technical analysis
pandas-ta>=0.3.14b
TA-Lib>=0.4.25

# Exchange connectivity
ccxt>=4.0.0
requests>=2.28.0
websockets>=11.0.0

# GUI Framework
PyQt5>=5.15.0
pyqtgraph>=0.13.0

# AI/ML Models
torch>=2.0.0
transformers>=4.30.0
openai>=1.0.0

# Configuration and utilities
PyYAML>=6.0.0
python-dateutil>=2.8.0
colorama>=0.4.6

# Async support
aiohttp>=3.8.0
asyncio-mqtt>=0.13.0

# Development and testing
pytest>=7.4.0
pytest-qt>=4.2.0
pytest-asyncio>=0.21.0

# Optional: GPU acceleration
# torch-audio>=2.0.0
# torch-vision>=0.15.0

# Optional: Enhanced performance
# uvloop>=0.19.0; sys_platform != "win32"
