#!/usr/bin/env python3
"""
Simple test script to check if the main application can be imported and run
"""

import sys
import traceback
import os

# Ensure we can see all output
sys.stdout.flush()
sys.stderr.flush()

try:
    print("Starting test script...")
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")

    print("Testing imports...")

    # Test PyQt5 import
    from PyQt5.QtWidgets import QApplication
    print("✓ PyQt5 imported successfully")

    # Test main application import
    print("Importing EpinnoxTradingInterface...")
    from launch_epinnox import EpinnoxTradingInterface
    print("✓ EpinnoxTradingInterface imported successfully")

    # Test application creation
    print("Creating QApplication...")
    app = QApplication(sys.argv)
    print("✓ QApplication created successfully")

    # Test main window creation step by step
    print("Creating EpinnoxTradingInterface...")
    sys.stdout.flush()
    window = EpinnoxTradingInterface()
    print("✓ EpinnoxTradingInterface created successfully")

    print("✓ All tests passed! Application should work.")

except AttributeError as e:
    print(f"❌ AttributeError: {e}")
    print("\nThis is likely the 'analysis_log' error we're looking for.")
    print("\nFull traceback:")
    traceback.print_exc()
except ImportError as e:
    print(f"❌ ImportError: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
except Exception as e:
    print(f"❌ Unexpected Error: {e}")
    print(f"Error type: {type(e)}")
    print("\nFull traceback:")
    traceback.print_exc()
finally:
    print("Test script completed.")
    sys.stdout.flush()
    sys.stderr.flush()
