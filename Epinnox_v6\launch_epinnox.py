#!/usr/bin/env python3
"""
Epinnox v6 Trading System Launcher
Clean launch script for the integrated trading interface
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 importsE
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    print("✓ PyQt5 loaded successfully")
except ImportError:
    print("✗ PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# Worker thread for non-blocking LLM analysis
class LLMAnalysisWorker(QObject):
    """Worker thread for LLM analysis to prevent UI blocking"""
    analysis_complete = pyqtSignal(dict)  # Signal when analysis is done
    error_occurred = pyqtSignal(str)  # Signal when error occurs

    def __init__(self, lmstudio_runner):
        super().__init__()
        self.lmstudio_runner = lmstudio_runner

    def run_analysis(self, prompt, temperature=0.7, max_tokens=200):
        """Run LLM analysis in background thread"""
        try:
            response = self.lmstudio_runner.run_inference(
                prompt=prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Emit result
            self.analysis_complete.emit({
                'response': response,
                'success': True
            })

        except Exception as e:
            self.error_occurred.emit(str(e))

# PyQtGraph for charting
try:
    import pyqtgraph as pg
    print("✓ PyQtGraph loaded successfully")
    # Configure PyQtGraph
    pg.setConfigOptions(useOpenGL=False, antialias=True)
except ImportError:
    print("✗ PyQtGraph not installed. Install with: pip install pyqtgraph")
    sys.exit(1)

# Load production configuration
try:
    from config.production_loader import (
        get_production_config, is_production_ready, get_live_trading_credentials,
        get_demo_mode_setting, enforce_production_settings
    )

    # Get production configuration
    prod_config = get_production_config()

    # Print production readiness status
    production_ready = prod_config.print_production_status()

    # Enforce production settings
    production_settings = enforce_production_settings()
    demo_mode = production_settings['demo_mode']

    # Initialize logging system
    try:
        from core.logging_config import initialize_logging, get_logger
        initialize_logging(debug_mode=production_settings.get('debug_mode', False))
        logger = get_logger('main')
        logger.info("Epinnox v6 starting up...")
    except Exception as e:
        print(f"⚠️ Logging initialization warning: {e}")
        logger = None

    # Initialize performance monitoring
    try:
        from core.performance_monitor import initialize_performance_monitor
        perf_monitor = initialize_performance_monitor(monitoring_interval=10)
        if logger:
            logger.info("Performance monitoring initialized")
    except Exception as e:
        print(f"⚠️ Performance monitoring warning: {e}")
        perf_monitor = None

    # Perform automatic cache cleanup
    try:
        from utils.cache_manager import cleanup_cache
        cache_stats = cleanup_cache()
        if cache_stats['files_removed'] > 0:
            print(f"🧹 Cache cleanup: removed {cache_stats['files_removed']} files, "
                  f"freed {cache_stats['bytes_freed'] / 1024 / 1024:.1f} MB")
            if logger:
                logger.info(f"Cache cleanup: removed {cache_stats['files_removed']} files")
    except Exception as e:
        print(f"⚠️ Cache cleanup warning: {e}")
        if logger:
            logger.warning(f"Cache cleanup failed: {e}")

    if not production_ready:
        print("\n⚠️ WARNING: System not fully configured for production!")
        print("   Please review the issues above before live trading.")

except ImportError as e:
    print(f"⚠️ Could not load production configuration: {e}")
    production_ready = False
    demo_mode = True  # Fallback to demo mode if config fails

# Try to import ccxt for real trading functionality
try:
    import ccxt
    print("✓ CCXT library available")

    # Initialize exchange (HTX/Huobi for LIVE TRADING)
    try:
        # Load real API credentials from production config
        creds = get_live_trading_credentials()

        # Validate credentials before using them
        if not creds['apiKey'] or not creds['secret']:
            raise ValueError("Missing HTX API credentials. Please check credentials.yaml")

        print(f"🔑 Loading HTX credentials...")
        print(f"   API Key: {creds['apiKey'][:8]}...{creds['apiKey'][-4:]}")
        print(f"   Secret: {'*' * len(creds['secret'])}")
        print(f"   Sandbox: {'ON' if creds.get('sandbox', False) else 'OFF'}")

        # Initialize HTX exchange with REAL credentials for LINEAR SWAP trading
        exchange_config = {
            'apiKey': creds['apiKey'],
            'secret': creds['secret'],
            'sandbox': creds.get('sandbox', False),  # Use sandbox setting from credentials
            'enableRateLimit': True,
            'timeout': 30000,  # 30 second timeout
            'rateLimit': 200,  # Rate limit in ms (increased to avoid HTX limits)
            'options': {
                'defaultType': 'swap',  # HTX Linear Swaps
                'marginMode': 'cross',  # Cross margin mode
                'fetchCurrencies': False,  # Disable problematic endpoint
            },
            'urls': {
                'api': {
                    'swap': 'https://api.hbdm.com',  # HTX Linear Swap API
                }
            }
        }

        # Add password if provided (some exchanges require it)
        if creds.get('password'):
            exchange_config['password'] = creds['password']

        # Use the correct exchange based on credentials
        exchange_name = creds.get('exchange', 'htx').lower()
        if exchange_name == 'huobi':
            exchange_name = 'htx'  # HTX is the new name for Huobi

        if exchange_name == 'htx':
            exchange = ccxt.htx(exchange_config)
        elif exchange_name == 'huobi':
            exchange = ccxt.huobi(exchange_config)
        else:
            raise ValueError(f"Unsupported exchange: {exchange_name}")
        demo_mode = False  # LIVE TRADING MODE

        # Test the connection
        try:
            exchange.load_markets()
            print("✅ HTX exchange initialized and connected successfully")
            print(f"🔑 Using API key: {creds['apiKey'][:8]}...")  # Show partial key for verification
        except Exception as test_error:
            print(f"❌ HTX connection test failed: {test_error}")
            raise
    except Exception as e:
        print(f"⚠ Could not initialize exchange: {e}")
        exchange = None
        demo_mode = True

    # Real trading functions using ccxt
    def place_limit_order(symbol, side, amount, price, params={}):
        """Place limit order using ccxt"""
        if exchange and not demo_mode:
            try:
                order = exchange.create_limit_order(symbol, side, amount, price, params)
                print(f"Real: Placed {side} limit order for {amount} {symbol} at {price}")
                return order
            except Exception as e:
                print(f"Error placing limit order: {e}")
                return None
        else:
            print(f"DEMO: Placed {side} limit order for {amount} {symbol} at {price}")
            return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params={}):
        """Place market order using ccxt"""
        if exchange and not demo_mode:
            try:
                order = exchange.create_market_order(symbol, side, amount, params)
                print(f"Real: Placed {side} market order for {amount} {symbol}")
                return order
            except Exception as e:
                print(f"Error placing market order: {e}")
                return None
        else:
            print(f"DEMO: Placed {side} market order for {amount} {symbol}")
            return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_ohlcv(symbol, timeframe="1m", limit=100):
        """Fetch OHLCV data using ccxt"""
        if exchange:
            try:
                return exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            except Exception as e:
                print(f"Error fetching OHLCV: {e}")
                return []  # Return empty data instead of mock
        else:
            print("No exchange available for OHLCV data")
            return []

    def fetch_order_book(symbol):
        """Fetch order book using ccxt"""
        if exchange:
            try:
                return exchange.fetch_order_book(symbol)
            except Exception as e:
                print(f"Error fetching order book: {e}")
                return {'bids': [], 'asks': []}  # Return empty orderbook instead of mock
        else:
            print("No exchange available for orderbook data")
            return {'bids': [], 'asks': []}

    def fetch_best_bid(symbol):
        """Fetch best bid using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'bids' in ob and ob['bids']:
                return ob['bids'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best bid: {e}")
            return None

    def fetch_best_ask(symbol):
        """Fetch best ask using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'asks' in ob and ob['asks']:
                return ob['asks'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best ask: {e}")
            return None

    # Removed mock data generation - using real data from live data manager

    def close_all_positions():
        """Close all positions - LIVE TRADING"""
        if exchange and not demo_mode:
            try:
                # Get all open positions
                positions = exchange.fetch_positions()
                closed_count = 0
                for position in positions:
                    if position['contracts'] > 0:  # Has open position
                        symbol = position['symbol']
                        side = 'sell' if position['side'] == 'long' else 'buy'
                        amount = position['contracts']
                        exchange.create_market_order(symbol, side, amount)
                        closed_count += 1
                print(f"LIVE: Closed {closed_count} positions")
                return True
            except Exception as e:
                print(f"Error closing positions: {e}")
                return False
        else:
            print("DEMO: Closing all positions")
            return True

    def cancel_all_orders():
        """Cancel all orders - LIVE TRADING"""
        if exchange and not demo_mode:
            try:
                cancelled_orders = exchange.cancel_all_orders()
                print(f"LIVE: Cancelled {len(cancelled_orders)} orders")
                return True
            except Exception as e:
                print(f"Error cancelling orders: {e}")
                return False
        else:
            print("DEMO: Cancelling all orders")
            return True

    def set_leverage(symbol, leverage):
        """Set leverage - LIVE TRADING"""
        if exchange and not demo_mode:
            try:
                exchange.set_leverage(leverage, symbol)
                print(f"LIVE: Set leverage {leverage}x for {symbol}")
                return True
            except Exception as e:
                print(f"Error setting leverage: {e}")
                return False
        else:
            print(f"DEMO: Set leverage {leverage}x for {symbol}")
            return True

except ImportError as e:
    print(f"⚠ Could not import trading functions: {e}")
    print("⚠ Using mock implementations")

    # Fallback mock implementations
    def place_limit_order(symbol, side, amount, price, params=None):
        """Mock limit order placement"""
        if params is None:
            params = {}
        print(f"MOCK: Placed {side} limit order for {amount} {symbol} at {price}")
        return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params=None):
        """Mock market order placement"""
        if params is None:
            params = {}
        print(f"MOCK: Placed {side} market order for {amount} {symbol}")
        return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_best_bid(symbol):
        """Mock best bid"""
        ob = fetch_order_book(symbol)
        return ob['bids'][0][0] if ob['bids'] else None

    def fetch_best_ask(symbol):
        """Mock best ask"""
        ob = fetch_order_book(symbol)
        return ob['asks'][0][0] if ob['asks'] else None

    def close_all_positions():
        """Mock close all positions"""
        print("MOCK: Closing all positions")
        return True

    def cancel_all_orders():
        """Mock cancel all orders"""
        print("MOCK: Cancelling all orders")
        return True

    def set_leverage(symbol, leverage):
        """Mock set leverage"""
        print(f"MOCK: Set leverage {leverage}x for {symbol}")
        return True

    exchange = None
    demo_mode = True

# Import GUI components
try:
    from gui.model_selector_widget import ModelSelectorWidget
    print("✓ Model selector widget loaded")
except ImportError as e:
    print(f"⚠ Could not import model selector widget: {e}")
    ModelSelectorWidget = None

# Matrix Theme
class MatrixTheme:
    """Matrix-inspired theme colors and styling"""
    
    # Colors
    BLACK = "#000000"
    BACKGROUND = "#000000"
    GREEN = "#00FF00"
    TEXT = "#00FF00"
    DARK_GREEN = "#003300"
    MID_GREEN = "#006600"
    LIGHT_GREEN = "#00CC00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    WHITE = "#FFFFFF"
    GRAY = "#666666"
    BRIGHT_GREEN = "#00FF88"

    # Font settings
    FONT_FAMILY = "Courier New"
    FONT_SIZE = 14
    FONT_SIZE_SMALL = 14
    FONT_SIZE_MEDIUM = 14
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: 'Courier New', monospace;
            font-size: {cls.FONT_SIZE_MEDIUM}px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {cls.DARK_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.GREEN};
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
            font-weight: bold;
        }}
        
        QGroupBox {{
            border: 2px solid {cls.DARK_GREEN};
            border-radius: 5px;
            margin: 5px;
            padding-top: 10px;
            color: {cls.GREEN};
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {cls.LIGHT_GREEN};
        }}
        
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
            padding: 8px 16px;
            font-weight: bold;
            border-radius: 3px;
        }}
        
        QPushButton:hover {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.LIGHT_GREEN};
            color: {cls.BLACK};
        }}
        
        QLabel {{
            color: {cls.GREEN};
            background-color: transparent;
        }}
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            font-family: 'Courier New', monospace;
        }}
        
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.DARK_GREEN};
            color: {cls.LIGHT_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 5px;
            border: 1px solid {cls.GREEN};
            font-weight: bold;
        }}
        
        QStatusBar {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-top: 1px solid {cls.DARK_GREEN};
        }}
        
        QCheckBox {{
            color: {cls.GREEN};
            spacing: 5px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
        }}
        
        QCheckBox::indicator:unchecked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.GREEN};
        }}
        """

# Simple Trading Interface
class EpinnoxTradingInterface(QMainWindow):
    """Simplified Epinnox Trading Interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(50, 50, 1400, 900)  # Larger window for better layout
        self.setMinimumSize(1200, 800)  # Minimum size to prevent cramping

        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())

        # Analysis control
        self.analysis_timer = None
        self.is_analyzing = False

        # Performance optimization: GUI update batching
        self.pending_gui_updates = {}
        self.gui_update_timer = QTimer()
        self.gui_update_timer.timeout.connect(self.apply_batched_gui_updates)
        self.gui_update_timer.setSingleShot(True)

        try:
            print("Setting up UI...")
            self.setup_ui()
            print("✓ UI setup complete")

            print("Setting up menu bar...")
            self.setup_menu_bar()
            print("✓ Menu bar setup complete")

            print("Setting up timers...")
            self.setup_timers()
            print("✓ Timers setup complete")

        except Exception as e:
            print(f"❌ Error during initialization: {e}")
            import traceback
            traceback.print_exc()
            raise

        # Verify menu bar exists
        menubar = self.menuBar()
        if menubar and menubar.actions():
            print(f"✓ Menu bar verified: {len(menubar.actions())} menu items")
            for action in menubar.actions():
                print(f"  - {action.text()}")
        else:
            print("❌ Menu bar missing or empty!")

        print("✓ Epinnox Trading Interface initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Main content (header is now in menu bar)
        content_widget = self.create_content()
        layout.addWidget(content_widget)

        # Status bar
        self.statusBar().showMessage("Epinnox v6 System Ready")

    def setup_menu_bar(self):
        """Setup menu bar with layout and settings options"""
        menubar = self.menuBar()

        # Ensure menu bar is visible
        menubar.setVisible(True)
        menubar.setNativeMenuBar(False)  # Force Qt menu bar instead of native

        # print("Creating menu bar...")

        # Layout menu
        layout_menu = menubar.addMenu('Layout')
        # print("✓ Layout menu created")

        # Save layout action
        save_layout_action = QAction('Save Layout', self)
        save_layout_action.setShortcut('Ctrl+S')
        save_layout_action.triggered.connect(self.save_layout)
        layout_menu.addAction(save_layout_action)

        # Load layout action
        load_layout_action = QAction('Load Layout', self)
        load_layout_action.setShortcut('Ctrl+L')
        load_layout_action.triggered.connect(self.load_layout)
        layout_menu.addAction(load_layout_action)

        layout_menu.addSeparator()

        # Reset to default layout
        reset_layout_action = QAction('Reset to Default', self)
        reset_layout_action.setShortcut('Ctrl+R')
        reset_layout_action.triggered.connect(self.reset_layout)
        layout_menu.addAction(reset_layout_action)

        # Settings menu
        settings_menu = menubar.addMenu('Settings')
        # print("✓ Settings menu created")

        # Model selection action
        model_settings_action = QAction('Model Selection', self)
        model_settings_action.setShortcut('Ctrl+M')
        model_settings_action.triggered.connect(self.show_model_settings)
        settings_menu.addAction(model_settings_action)

        settings_menu.addSeparator()

        # Preferences action
        preferences_action = QAction('Preferences', self)
        preferences_action.triggered.connect(self.show_preferences)
        settings_menu.addAction(preferences_action)

        # About menu
        about_menu = menubar.addMenu('About')
        print("✓ About menu created")

        # About Epinnox action
        about_action = QAction('About Epinnox', self)
        about_action.triggered.connect(self.show_about)
        about_menu.addAction(about_action)

        # Add status widgets to the right side of menu bar
        from PyQt5.QtCore import QTimer


        # Create compact status widget container
        status_widget = QWidget()
        status_widget.setMaximumWidth(650)  # Increased width to fit all status info
        status_widget.setMinimumWidth(500)  # Ensure minimum width
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 2, 5, 5)
        status_layout.setSpacing(8)  # Slightly more spacing for readability

        # System title (shorter)
        title_label = QLabel("EPINNOX v6")
        title_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(title_label)

        # System status
        self.system_status_label = QLabel("READY")
        self.system_status_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(self.system_status_label)

        # Production mode indicator
        mode_text = "🔴 LIVE" if not demo_mode else "🟡 DEMO"
        mode_color = MatrixTheme.RED if not demo_mode else MatrixTheme.YELLOW
        self.mode_label = QLabel(mode_text)
        self.mode_label.setStyleSheet(f"""
            color: {mode_color};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            padding: 2px 6px;
            border: 1px solid {mode_color};
            border-radius: 3px;
        """)
        status_layout.addWidget(self.mode_label)

        # Balance display (like in your image: Equity: $209.34 Free: $209.34)
        self.balance_label = QLabel("Equity: $-- Free: $--")
        self.balance_label.setMinimumWidth(200)  # Ensure enough space for balance text
        self.balance_label.setStyleSheet(f"""
            color: {MatrixTheme.YELLOW};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            padding: 2px 6px;
            border: 1px solid {MatrixTheme.YELLOW};
            border-radius: 3px;
            background-color: rgba(255, 255, 0, 0.1);
        """)
        status_layout.addWidget(self.balance_label)

        # Current time
        self.time_label = QLabel()
        self.time_label.setMinimumWidth(150)  # Ensure enough space for timestamp
        self.time_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
        """)
        status_layout.addWidget(self.time_label)

        # Add status widget to menu bar (right side)
        menubar.setCornerWidget(status_widget, Qt.TopRightCorner)

        # Setup timer to update time (reduced frequency to prevent lag)
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(5000)  # Update every 5 seconds (reduced from 1 second)
        self.update_time_display()  # Initial update

        # Style menu bar - ensure menu items are visible with high contrast
        print("Applying menu bar styling...")
        menubar.setStyleSheet(f"""
            QMenuBar {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                border-bottom: 2px solid {MatrixTheme.GREEN};
                padding: 4px;
                min-height: 25px;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                font-weight: bold;
            }}
            QMenuBar::item {{
                background-color: transparent;
                color: {MatrixTheme.GREEN};
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
                border: 1px solid transparent;
            }}
            QMenuBar::item:selected {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.GREEN};
            }}
            QMenuBar::item:pressed {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
            }}
            QMenu {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                border: 2px solid {MatrixTheme.GREEN};
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 4px;
            }}
            QMenu::item {{
                padding: 8px 24px;
                background-color: transparent;
                color: {MatrixTheme.GREEN};
                border: 1px solid transparent;
            }}
            QMenu::item:selected {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.GREEN};
            }}
        """)

        print("Menu bar setup complete")  # Debug

    def save_layout(self):
        """Save current layout to settings"""
        try:
            settings = QSettings('Epinnox', 'TradingInterface')

            # Save main splitter state
            if hasattr(self, 'main_splitter'):
                settings.setValue('main_splitter', self.main_splitter.saveState())

            # Save individual column splitter states
            if hasattr(self, 'left_splitter'):
                settings.setValue('left_splitter', self.left_splitter.saveState())
            if hasattr(self, 'middle_splitter'):
                settings.setValue('middle_splitter', self.middle_splitter.saveState())
            if hasattr(self, 'right_splitter'):
                settings.setValue('right_splitter', self.right_splitter.saveState())

            # Save window geometry
            settings.setValue('geometry', self.saveGeometry())
            settings.setValue('windowState', self.saveState())

            self.statusBar().showMessage("Layout saved successfully", 2000)
            print("✓ Layout saved to settings")

        except Exception as e:
            print(f"Error saving layout: {e}")
            self.statusBar().showMessage("Error saving layout", 2000)

    def load_layout(self):
        """Load layout from settings"""
        try:
            settings = QSettings('Epinnox', 'TradingInterface')

            # Restore main splitter state
            if hasattr(self, 'main_splitter'):
                state = settings.value('main_splitter')
                if state:
                    self.main_splitter.restoreState(state)

            # Restore individual column splitter states
            if hasattr(self, 'left_splitter'):
                state = settings.value('left_splitter')
                if state:
                    self.left_splitter.restoreState(state)
            if hasattr(self, 'middle_splitter'):
                state = settings.value('middle_splitter')
                if state:
                    self.middle_splitter.restoreState(state)
            if hasattr(self, 'right_splitter'):
                state = settings.value('right_splitter')
                if state:
                    self.right_splitter.restoreState(state)

            # Restore window geometry
            geometry = settings.value('geometry')
            if geometry:
                self.restoreGeometry(geometry)
            window_state = settings.value('windowState')
            if window_state:
                self.restoreState(window_state)

            self.statusBar().showMessage("Layout loaded successfully", 2000)
            print("✓ Layout loaded from settings")

        except Exception as e:
            print(f"Error loading layout: {e}")
            self.statusBar().showMessage("Error loading layout", 2000)

    def reset_layout(self):
        """Reset to default layout"""
        try:
            # Reset main splitter to default sizes
            if hasattr(self, 'main_splitter'):
                self.main_splitter.setSizes([400, 450, 600])

            # Reset column splitters to default sizes
            if hasattr(self, 'left_splitter'):
                self.left_splitter.setSizes([120, 100, 300, 150])
            if hasattr(self, 'middle_splitter'):
                self.middle_splitter.setSizes([150, 200, 150, 200])
            if hasattr(self, 'right_splitter'):
                self.right_splitter.setSizes([250, 400])

            # Reset window size
            self.setGeometry(50, 50, 1400, 900)

            self.statusBar().showMessage("Layout reset to default", 2000)
            print("✓ Layout reset to default")

        except Exception as e:
            print(f"Error resetting layout: {e}")
            self.statusBar().showMessage("Error resetting layout", 2000)

    def show_model_settings(self):
        """Show model selection settings dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

            dialog = QDialog(self)
            dialog.setWindowTitle("Model Selection Settings")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # Add model selector widget to dialog
            if hasattr(self, 'model_selector') and self.model_selector:
                # Move existing model selector to dialog temporarily
                model_widget = self.model_selector
            elif ModelSelectorWidget:
                # Create new model selector for dialog
                model_widget = ModelSelectorWidget()
                model_widget.model_switch_requested.connect(self.on_model_switch_requested)
                model_widget.refresh_requested.connect(self.on_model_refresh_requested)
            else:
                # Fallback label
                model_widget = QLabel("Model selector not available")

            layout.addWidget(model_widget)

            # Buttons
            button_layout = QHBoxLayout()
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing model settings: {e}")

    def show_preferences(self):
        """Show preferences dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QCheckBox

            dialog = QDialog(self)
            dialog.setWindowTitle("Preferences")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # Add some preference options
            layout.addWidget(QLabel("Trading Preferences:"))

            auto_trade_cb = QCheckBox("Enable Auto Trading")
            layout.addWidget(auto_trade_cb)

            debug_mode_cb = QCheckBox("Debug Mode")
            layout.addWidget(debug_mode_cb)

            layout.addStretch()

            # Buttons
            button_layout = QHBoxLayout()
            save_btn = QPushButton("Save")
            cancel_btn = QPushButton("Cancel")
            save_btn.clicked.connect(dialog.accept)
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addStretch()
            button_layout.addWidget(save_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
                QCheckBox {{
                    color: {MatrixTheme.TEXT};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing preferences: {e}")

    def show_about(self):
        """Show about dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
            from PyQt5.QtCore import Qt

            dialog = QDialog(self)
            dialog.setWindowTitle("About Epinnox")
            dialog.setModal(True)
            dialog.resize(500, 350)

            layout = QVBoxLayout(dialog)

            # Title
            title = QLabel("Epinnox v6 Trading System")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet(f"""
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                font-weight: bold;
                color: {MatrixTheme.GREEN};
                margin: 10px;
            """)
            layout.addWidget(title)

            # Description
            description = QLabel("""
            Advanced AI-Powered Trading System

            Features:
            • Real-time market data analysis
            • ML/LLM signal integration
            • Dynamic leverage management
            • Professional trading interface
            • Comprehensive risk management

            Combining numerical analysis with language models
            for intelligent trading decisions.
            """)
            description.setAlignment(Qt.AlignCenter)
            description.setWordWrap(True)
            layout.addWidget(description)

            layout.addStretch()

            # Close button
            button_layout = QHBoxLayout()
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
                QLabel {{
                    color: {MatrixTheme.TEXT};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing about dialog: {e}")

    def update_time_display(self):
        """Update the time display in the menu bar"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.setText(current_time)
        except Exception as e:
            print(f"Error updating time display: {e}")

    def update_balance_display(self):
        """Update the balance display in the menu bar"""
        try:
            # Fetch balance from real trading interface
            if hasattr(self, 'real_trading') and self.real_trading:
                balance = self.real_trading.get_balance_info()

                if balance:
                    usdt_info = balance.get('USDT', {})
                    free_balance = usdt_info.get('free', 0)
                    total_balance = usdt_info.get('total', 0)

                    # Update menu bar balance display
                    if hasattr(self, 'balance_label'):
                        balance_text = f"Equity: ${total_balance:.2f} Free: ${free_balance:.2f}"
                        self.balance_label.setText(balance_text)

                        # Color coding based on balance
                        if free_balance > 0:
                            color = MatrixTheme.YELLOW  # Yellow for positive balance
                        else:
                            color = MatrixTheme.RED  # Red for zero/negative balance

                        self.balance_label.setStyleSheet(f"""
                            color: {color};
                            font-weight: bold;
                            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                            padding: 2px 6px;
                            border: 1px solid {color};
                            border-radius: 3px;
                            background-color: rgba(255, 255, 0, 0.1);
                        """)
        except Exception as e:
            print(f"❌ Error updating balance display: {e}")
    
    def create_content(self):
        """Create main content area with resizable splitters"""
        # Main horizontal splitter
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setChildrenCollapsible(False)  # Prevent panels from collapsing completely

        # Left column - Controls and Analysis
        self.left_splitter = self.create_left_column()
        self.main_splitter.addWidget(self.left_splitter)

        # Middle column - Analysis Results
        self.middle_splitter = self.create_middle_column()
        self.main_splitter.addWidget(self.middle_splitter)

        # Right column - Chart and Trading
        self.right_splitter = self.create_right_column()
        self.main_splitter.addWidget(self.right_splitter)

        # Set initial sizes (proportional)
        self.main_splitter.setSizes([400, 450, 600])  # Left, Middle, Right widths

        # Style the splitter
        self.main_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.MID_GREEN};
                width: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.GREEN};
            }}
        """)

        # Create container widget
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.main_splitter)

        return container

    def create_left_column(self):
        """Create left column with resizable panels"""
        # Create vertical splitter for left column panels
        left_splitter = QSplitter(Qt.Vertical)
        left_splitter.setChildrenCollapsible(False)

        # Symbol selection and controls
        symbol_panel = self.create_symbol_panel()
        left_splitter.addWidget(symbol_panel)

        # Current analysis
        current_analysis_panel = self.create_current_analysis_panel()
        left_splitter.addWidget(current_analysis_panel)

        # LLM Analysis (expanded)
        llm_panel = self.create_llm_analysis_panel()
        left_splitter.addWidget(llm_panel)

        # Risk Warnings (compact)
        risk_warnings_panel = self.create_risk_warnings_panel()
        left_splitter.addWidget(risk_warnings_panel)

        # Set initial sizes for left column panels (more space for LLM analysis)
        left_splitter.setSizes([100, 80, 450, 120])  # Symbol, Current, LLM (expanded), Risk

        # Style the vertical splitter
        left_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return left_splitter

    def create_middle_column(self):
        """Create middle column with resizable panels"""
        # Create vertical splitter for middle column panels
        middle_splitter = QSplitter(Qt.Vertical)
        middle_splitter.setChildrenCollapsible(False)

        # ML Models Status (compact table)
        ml_models_panel = self.create_ml_models_panel()
        middle_splitter.addWidget(ml_models_panel)

        # Final Trading Verdict (moved to middle column)
        final_verdict_panel = self.create_final_verdict_panel()
        middle_splitter.addWidget(final_verdict_panel)

        # Market Analysis (compact)
        market_analysis_panel = self.create_market_analysis_panel()
        middle_splitter.addWidget(market_analysis_panel)

        # Set initial sizes for middle column panels (ML models, Final Verdict, Market)
        middle_splitter.setSizes([300, 250, 200])  # ML (300px), Final Verdict (250px), Market (200px)

        # Style the vertical splitter
        middle_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return middle_splitter

    def create_right_column(self):
        """Create right column with resizable panels"""
        # Create vertical splitter for right column panels
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setChildrenCollapsible(False)

        # Manual Trading Controls (compact)
        trading_panel = self.create_manual_trading_panel()
        right_splitter.addWidget(trading_panel)

        # Leverage Analysis Panel (compact)
        leverage_panel = self.create_leverage_panel()
        right_splitter.addWidget(leverage_panel)

        # Historical Final Verdicts Panel
        historical_verdicts_panel = self.create_historical_verdicts_panel()
        right_splitter.addWidget(historical_verdicts_panel)

        # Chart Panel (expandable)
        chart_panel = self.create_chart_panel()
        right_splitter.addWidget(chart_panel)

        # Set initial sizes for right column panels
        right_splitter.setSizes([200, 150, 200, 300])  # Trading: 200px, Leverage: 150px, History: 200px, Chart: 300px

        # Style the vertical splitter
        right_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return right_splitter

    def create_symbol_panel(self):
        """Create symbol selection panel"""
        group = QGroupBox("Symbol Selection")
        layout = QVBoxLayout(group)

        # Trading Symbol
        symbol_layout = QHBoxLayout()
        symbol_layout.addWidget(QLabel("Trading Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT"])
        symbol_layout.addWidget(self.symbol_combo)
        layout.addLayout(symbol_layout)

        # Checkboxes
        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.live_data_checkbox.setChecked(True)
        layout.addWidget(self.live_data_checkbox)

        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (60s)")
        self.auto_refresh_checkbox.setChecked(True)
        layout.addWidget(self.auto_refresh_checkbox)

        # Autonomous Trading checkbox
        self.auto_trader_checkbox = QCheckBox("🤖 Auto Trader")
        self.auto_trader_checkbox.setChecked(False)
        self.auto_trader_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {MatrixTheme.YELLOW};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            }}
            QCheckBox::indicator:checked {{
                background-color: {MatrixTheme.GREEN};
                border: 2px solid {MatrixTheme.GREEN};
            }}
            QCheckBox::indicator:unchecked {{
                background-color: transparent;
                border: 2px solid {MatrixTheme.GRAY};
            }}
        """)
        self.auto_trader_checkbox.stateChanged.connect(self.on_auto_trader_toggled)
        layout.addWidget(self.auto_trader_checkbox)

        # Buttons
        self.analyze_button = QPushButton("ANALYZE SYMBOL")
        self.analyze_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.analyze_button)

        self.stop_button = QPushButton("STOP ANALYSIS")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_analysis)
        layout.addWidget(self.stop_button)

        return group

    def create_current_analysis_panel(self):
        """Create current analysis panel"""
        group = QGroupBox("Current Analysis")
        layout = QVBoxLayout(group)

        self.decision_label = QLabel("Decision: WAIT")
        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.decision_label)

        self.confidence_label = QLabel("Confidence: 86.0%")
        layout.addWidget(self.confidence_label)

        self.last_update_label = QLabel("Last Update: 19:32:58")
        layout.addWidget(self.last_update_label)

        return group

    def create_ml_models_panel(self):
        """Create ML models status panel"""
        group = QGroupBox("ML Models Status")
        layout = QVBoxLayout(group)

        # Create table with 4 columns and 8 rows for all ML models
        self.ml_models_table = QTableWidget(8, 4)  # Increased from 3 to 8 rows
        # ─ Stretch columns ─
        header = self.ml_models_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # ─ Transparent viewport & dark scrollbar ─
        self.ml_models_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.ml_models_table.setHorizontalHeaderLabels(["Model", "Decision", "Confidence", "Actual Confidence"])
        self.ml_models_table.verticalHeader().setVisible(False)

        # Make table fill the entire container height with minimum height for all rows
        self.ml_models_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.ml_models_table.setMinimumHeight(250)  # Ensure enough height for 8 rows
        self.ml_models_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.ml_models_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Add comprehensive ML models data
        models_data = [
            ["SVM", "WAIT", "00.00%", "--"],
            ["Random Forest", "WAIT", "00.00%", "--"],
            ["LSTM", "WAIT", "00.00%", "--"],
            ["RSI Model", "WAIT", "00.00%", "--"],
            ["VWAP Model", "WAIT", "00.00%", "--"],
            ["Orderflow Model", "WAIT", "00.00%", "--"],
            ["Volatility Model", "WAIT", "00.00%", "--"],
            ["Sentiment Model", "WAIT", "00.00%", "--"]
        ]

        for row, (model, decision, confidence, actual_confidence) in enumerate(models_data):
            # Model name
            model_item = QTableWidgetItem(model)
            model_item.setForeground(QColor(MatrixTheme.TEXT))
            self.ml_models_table.setItem(row, 0, model_item)

            # Decision with color coding
            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)

            # Confidence percentage
            confidence_item = QTableWidgetItem(confidence)
            confidence_item.setForeground(QColor(MatrixTheme.TEXT))
            self.ml_models_table.setItem(row, 2, confidence_item)

            # Actual confidence column with placeholder
            actual_conf_item = QTableWidgetItem(actual_confidence)
            actual_conf_item.setForeground(QColor(MatrixTheme.GRAY))
            self.ml_models_table.setItem(row, 3, actual_conf_item)

        layout.addWidget(self.ml_models_table)

        return group

    def create_llm_analysis_panel(self):
        """Create LLM analysis panel with expanded text area"""
        group = QGroupBox("LLM Analysis")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)

        # LLM Decision
        self.llm_decision_label = QLabel("LLM Decision: WAIT")
        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.llm_decision_label)

        # LLM Confidence
        self.llm_confidence_label = QLabel("LLM Confidence: 75.0%")
        layout.addWidget(self.llm_confidence_label)

        # LLM Reasoning (scrollable)
        reasoning_label = QLabel("LLM Reasoning:")
        layout.addWidget(reasoning_label)

        self.llm_reasoning_text = QTextEdit()
        self.llm_reasoning_text.setReadOnly(True)
        self.llm_reasoning_text.setMaximumHeight(200)  # Increased from 100
        self.llm_reasoning_text.setMinimumHeight(120)  # Increased from 60
        self.llm_reasoning_text.setWordWrapMode(QTextOption.WordWrap)

        # Ensure it expands to fill available space
        self.llm_reasoning_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Apply Matrix theme scrollbar styling
        self.llm_reasoning_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        self.llm_reasoning_text.setText("Market shows mixed signals. Technical indicators suggest consolidation while volume patterns indicate potential breakout. Recommend WAIT for clearer direction.")
        layout.addWidget(self.llm_reasoning_text)

        # Comprehensive Analysis Panel
        comprehensive_group = QGroupBox("🎭 Comprehensive Creative Analysis")
        comprehensive_layout = QVBoxLayout(comprehensive_group)

        # Comprehensive decision display
        self.comprehensive_decision_label = QLabel("Comprehensive Decision: Analyzing...")
        self.comprehensive_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 5px;
            border: 1px solid {MatrixTheme.GREEN};
            border-radius: 5px;
            background-color: {MatrixTheme.BLACK};
        """)
        comprehensive_layout.addWidget(self.comprehensive_decision_label)

        # Creative insight display
        self.creative_insight_text = QTextEdit()
        self.creative_insight_text.setMaximumHeight(120)
        self.creative_insight_text.setMinimumHeight(80)
        self.creative_insight_text.setReadOnly(True)
        self.creative_insight_text.setWordWrapMode(QTextOption.WordWrap)
        self.creative_insight_text.setPlaceholderText("Creative market insights will appear here...")
        self.creative_insight_text.setStyleSheet(f"""
            background-color: {MatrixTheme.BLACK};
            color: {MatrixTheme.GREEN};
            border: 1px solid {MatrixTheme.DARK_GREEN};
            font-family: {MatrixTheme.FONT_FAMILY};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 5px;
        """)
        comprehensive_layout.addWidget(self.creative_insight_text)

        layout.addWidget(comprehensive_group)

        return group

    def create_final_verdict_panel(self):
        """Create Final Trading Verdict panel for middle column"""
        group = QGroupBox("🎯 Final Trading Verdict")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Final verdict display
        self.final_verdict_label = QLabel("Final Verdict: Awaiting Analysis...")
        self.final_verdict_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 8px;
            border: 2px solid {MatrixTheme.YELLOW};
            border-radius: 5px;
            background-color: {MatrixTheme.BLACK};
            text-align: center;
        """)
        layout.addWidget(self.final_verdict_label)

        # Trading parameters display in compact grid
        params_grid = QGridLayout()
        params_grid.setSpacing(4)

        # Position size
        params_grid.addWidget(QLabel("Position Size:"), 0, 0)
        self.final_position_size_label = QLabel("$0.00")
        self.final_position_size_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_position_size_label, 0, 1)

        # Entry price
        params_grid.addWidget(QLabel("Entry Price:"), 1, 0)
        self.final_entry_price_label = QLabel("$0.000000")
        self.final_entry_price_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_entry_price_label, 1, 1)

        # Stop loss
        params_grid.addWidget(QLabel("Stop Loss:"), 2, 0)
        self.final_stop_loss_label = QLabel("$0.000000")
        self.final_stop_loss_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_stop_loss_label, 2, 1)

        # Take profit
        params_grid.addWidget(QLabel("Take Profit:"), 3, 0)
        self.final_take_profit_label = QLabel("$0.000000")
        self.final_take_profit_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_take_profit_label, 3, 1)

        # Leverage
        params_grid.addWidget(QLabel("Leverage:"), 0, 2)
        self.final_leverage_label = QLabel("1x")
        self.final_leverage_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_leverage_label, 0, 3)

        # Risk level
        params_grid.addWidget(QLabel("Risk Level:"), 1, 2)
        self.final_risk_level_label = QLabel("LOW")
        self.final_risk_level_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_risk_level_label, 1, 3)

        # Confidence
        params_grid.addWidget(QLabel("Confidence:"), 2, 2)
        self.final_confidence_label = QLabel("0%")
        self.final_confidence_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        params_grid.addWidget(self.final_confidence_label, 2, 3)

        layout.addLayout(params_grid)

        # Final reasoning - compact
        reasoning_label = QLabel("Final Reasoning:")
        reasoning_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        layout.addWidget(reasoning_label)

        self.final_reasoning_text = QTextEdit()
        self.final_reasoning_text.setReadOnly(True)
        self.final_reasoning_text.setMaximumHeight(80)
        self.final_reasoning_text.setMinimumHeight(60)
        self.final_reasoning_text.setWordWrapMode(QTextOption.WordWrap)
        self.final_reasoning_text.setPlaceholderText("Final trading reasoning will appear here...")
        self.final_reasoning_text.setStyleSheet(f"""
            background-color: {MatrixTheme.BLACK};
            color: {MatrixTheme.GREEN};
            border: 1px solid {MatrixTheme.DARK_GREEN};
            font-family: {MatrixTheme.FONT_FAMILY};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 5px;
        """)
        layout.addWidget(self.final_reasoning_text)

        return group

    def create_historical_verdicts_panel(self):
        """Create historical final verdicts panel"""
        group = QGroupBox("📊 Historical Final Verdicts")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Create table for historical verdicts with enhanced columns
        self.historical_verdicts_table = QTableWidget(0, 8)  # 8 columns: Time, Symbol, Verdict, Entry, Exit, PnL, Result, Duration

        # Set headers
        self.historical_verdicts_table.setHorizontalHeaderLabels([
            "Time", "Symbol", "Verdict", "Entry", "Exit", "PnL%", "Result", "Duration"
        ])

        # Configure table appearance
        header = self.historical_verdicts_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        self.historical_verdicts_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
                padding: 3px;
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.historical_verdicts_table.verticalHeader().setVisible(False)
        self.historical_verdicts_table.setAlternatingRowColors(True)
        self.historical_verdicts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.historical_verdicts_table.setMinimumHeight(150)
        self.historical_verdicts_table.setMaximumHeight(200)

        layout.addWidget(self.historical_verdicts_table)

        # Summary stats
        stats_layout = QHBoxLayout()

        self.total_verdicts_label = QLabel("Total: 0")
        self.total_verdicts_label.setStyleSheet(f"color: {MatrixTheme.TEXT}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        stats_layout.addWidget(self.total_verdicts_label)

        self.success_rate_label = QLabel("Success: 0%")
        self.success_rate_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        stats_layout.addWidget(self.success_rate_label)

        self.avg_confidence_label = QLabel("Avg Conf: 0%")
        self.avg_confidence_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
        stats_layout.addWidget(self.avg_confidence_label)

        layout.addLayout(stats_layout)

        # Initialize historical verdicts storage
        self.historical_verdicts = []

        # Initialize verdict tracking system
        self.active_verdicts = {}  # Track verdicts that are still being monitored
        self.verdict_tracking_timer = QTimer()
        self.verdict_tracking_timer.timeout.connect(self.update_verdict_tracking)
        self.verdict_tracking_timer.start(5000)  # Check every 5 seconds

        # Initialize autonomous trading system
        self.autonomous_trading_enabled = False
        self.autonomous_trading_stats = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_pnl': 0.0,
            'daily_trades': 0,
            'last_trade_time': None,
            'max_daily_trades': 10,  # Safety limit
            'max_drawdown_limit': 15.0,  # 15% max drawdown
            'emergency_stop_triggered': False
        }

        return group

    def create_leverage_panel(self):
        """Create leverage analysis panel"""
        group = QGroupBox("Leverage Analysis")
        layout = QVBoxLayout(group)

        self.max_leverage_label = QLabel("Max Available: 1.0x")
        layout.addWidget(self.max_leverage_label)

        self.recommended_leverage_label = QLabel("Recommended: 1.0x")
        layout.addWidget(self.recommended_leverage_label)

        self.effective_leverage_label = QLabel("Effective: 0.4x")
        layout.addWidget(self.effective_leverage_label)

        self.position_size_label = QLabel("Position Size: 0.00 units ($0.00)")
        layout.addWidget(self.position_size_label)

        self.risk_per_trade_label = QLabel("Risk per Trade: $0.00")
        layout.addWidget(self.risk_per_trade_label)

        return group
    


    def create_market_analysis_panel(self):
        """Create market analysis panel"""
        group = QGroupBox("Market Analysis")
        layout = QVBoxLayout(group)

        self.market_regime_label = QLabel("Market Regime: ----")
        layout.addWidget(self.market_regime_label)

        self.trend_strength_label = QLabel("Trend Strength: 0.00")
        layout.addWidget(self.trend_strength_label)

        self.volatility_label = QLabel("Volatility: 0.00%")
        layout.addWidget(self.volatility_label)

        self.liquidity_score_label = QLabel("Liquidity Score: --")
        layout.addWidget(self.liquidity_score_label)

        return group



    def create_risk_warnings_panel(self):
        """Create comprehensive risk warnings panel"""
        group = QGroupBox("Risk Management & Warnings")
        layout = QVBoxLayout(group)

        # Risk Metrics
        metrics_layout = QGridLayout()

        # Row 1: Portfolio Risk
        metrics_layout.addWidget(QLabel("Portfolio Risk:"), 0, 0)
        self.portfolio_risk_label = QLabel("2.5%")
        self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.portfolio_risk_label, 0, 1)

        # Row 2: Max Drawdown
        metrics_layout.addWidget(QLabel("Max Drawdown:"), 1, 0)
        self.max_drawdown_label = QLabel("5.2%")
        self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.max_drawdown_label, 1, 1)

        # Row 3: Correlation Risk
        metrics_layout.addWidget(QLabel("Correlation Risk:"), 2, 0)
        self.correlation_risk_label = QLabel("LOW")
        self.correlation_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.correlation_risk_label, 2, 1)

        # Row 4: Liquidity Risk
        metrics_layout.addWidget(QLabel("Liquidity Risk:"), 3, 0)
        self.liquidity_risk_label = QLabel("MEDIUM")
        self.liquidity_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.liquidity_risk_label, 3, 1)

        layout.addLayout(metrics_layout)

        # Active Warnings
        warnings_label = QLabel("Active Warnings:")
        warnings_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        layout.addWidget(warnings_label)

        self.risk_warnings_log = QTextEdit()
        self.risk_warnings_log.setReadOnly(True)
        self.risk_warnings_log.setMaximumHeight(100)

        # Apply Matrix theme scrollbar styling
        self.risk_warnings_log.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        # Add comprehensive sample warnings
        sample_warnings = [
            "⚠️ HIGH VOLATILITY: 24h volatility >5% - reduce position size",
            "⚠️ LOW LIQUIDITY: Order book depth <$50k - limit order size",
            "⚠️ CORRELATION ALERT: 0.85 correlation with BTC - diversify",
            "⚠️ LEVERAGE WARNING: Current 3.2x exceeds recommended 2.5x"
        ]

        for warning in sample_warnings:
            self.risk_warnings_log.append(warning)

        layout.addWidget(self.risk_warnings_log)

        return group

    def create_manual_trading_panel(self):
        """Create compact manual trading controls panel"""
        group = QGroupBox("Manual Trading")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)
        layout.setContentsMargins(5, 2, 5, 5)

        # Trading parameters - more compact
        params_layout = QGridLayout()
        params_layout.setSpacing(4)

        # Quantity - smaller spinbox
        params_layout.addWidget(QLabel("Quantity:"), 0, 0)
        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setRange(0.0001, 100000)
        self.quantity_spinbox.setDecimals(4)
        self.quantity_spinbox.setValue(50.0)
        self.quantity_spinbox.setMaximumHeight(25)  # Make more compact
        params_layout.addWidget(self.quantity_spinbox, 0, 1)

        # Leverage - smaller spinbox
        params_layout.addWidget(QLabel("Leverage:"), 1, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 125)
        self.leverage_spinbox.setValue(20)
        self.leverage_spinbox.setMaximumHeight(25)  # Make more compact
        # Connect leverage change to update trading interface
        self.leverage_spinbox.valueChanged.connect(self.on_leverage_changed)
        params_layout.addWidget(self.leverage_spinbox, 1, 1)

        # Compact Bid/Ask display
        params_layout.addWidget(QLabel("Best Bid:"), 2, 0)
        self.best_bid_label = QLabel("--")
        self.best_bid_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px;
                background-color: rgba(0, 255, 68, 0.1);
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 2px;
                max-height: 20px;
            }}
        """)
        params_layout.addWidget(self.best_bid_label, 2, 1)

        params_layout.addWidget(QLabel("Best Ask:"), 3, 0)
        self.best_ask_label = QLabel("--")
        self.best_ask_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.RED};
                font-weight: bold;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px;
                background-color: rgba(255, 0, 0, 0.1);
                border: 1px solid {MatrixTheme.RED};
                border-radius: 2px;
                max-height: 20px;
            }}
        """)
        params_layout.addWidget(self.best_ask_label, 3, 1)

        # Compact spread display
        params_layout.addWidget(QLabel("Spread:"), 4, 0)
        self.spread_label = QLabel("--")
        self.spread_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.YELLOW};
                font-weight: bold;
                padding: 2px;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                max-height: 18px;
            }}
        """)
        params_layout.addWidget(self.spread_label, 4, 1)

        # Price input for limit orders
        params_layout.addWidget(QLabel("Limit Price:"), 5, 0)
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.000001, 999999)
        self.price_spinbox.setDecimals(6)
        self.price_spinbox.setValue(0.175)
        self.price_spinbox.setToolTip("Price for limit orders (auto-filled from bid/ask)")
        params_layout.addWidget(self.price_spinbox, 5, 1)

        # Auto-fill price buttons
        price_buttons_layout = QHBoxLayout()

        fill_bid_btn = QPushButton("Use Bid")
        fill_bid_btn.setMaximumWidth(60)
        fill_bid_btn.clicked.connect(self.fill_bid_price)
        fill_bid_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)

        fill_ask_btn = QPushButton("Use Ask")
        fill_ask_btn.setMaximumWidth(60)
        fill_ask_btn.clicked.connect(self.fill_ask_price)
        fill_ask_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.BLACK};
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)

        price_buttons_layout.addWidget(fill_bid_btn)
        price_buttons_layout.addWidget(fill_ask_btn)
        price_buttons_layout.addStretch()

        params_layout.addLayout(price_buttons_layout, 6, 0, 1, 2)

        # Trading mode status (removed duplicate - mode is already shown in menu bar)

        # Initialize bid/ask tracking
        self.current_bid = None
        self.current_ask = None
        self.last_bid = None
        self.last_ask = None

        layout.addLayout(params_layout)

        # Compact trading buttons
        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(4)

        # Long buttons (green) - more compact
        self.limit_long_btn = QPushButton("LIMIT LONG")
        self.limit_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #006600;
                color: white;
                font-weight: bold;
                padding: 4px;
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 2px;
                font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.limit_long_btn.clicked.connect(self.place_limit_long)
        buttons_layout.addWidget(self.limit_long_btn, 0, 0)

        self.market_long_btn = QPushButton("MARKET LONG")
        self.market_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #009900;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GREEN};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.market_long_btn.clicked.connect(self.place_market_long)
        buttons_layout.addWidget(self.market_long_btn, 0, 1)

        # Short buttons (red)
        self.limit_short_btn = QPushButton("LIMIT SHORT")
        self.limit_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #660000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.limit_short_btn.clicked.connect(self.place_limit_short)
        buttons_layout.addWidget(self.limit_short_btn, 1, 0)

        self.market_short_btn = QPushButton("MARKET SHORT")
        self.market_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #990000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.market_short_btn.clicked.connect(self.place_market_short)
        buttons_layout.addWidget(self.market_short_btn, 1, 1)

        # Control buttons
        self.close_all_btn = QPushButton("CLOSE ALL")
        self.close_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #ff6600;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.YELLOW};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.YELLOW};
                color: black;
            }}
        """)
        self.close_all_btn.clicked.connect(self.close_all_positions)
        buttons_layout.addWidget(self.close_all_btn, 2, 0)

        self.cancel_all_btn = QPushButton("CANCEL ALL")
        self.cancel_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #666666;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GRAY};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GRAY};
                color: white;
            }}
        """)
        self.cancel_all_btn.clicked.connect(self.cancel_all_orders)
        buttons_layout.addWidget(self.cancel_all_btn, 2, 1)

        layout.addLayout(buttons_layout)

        return group

    def create_chart_panel(self):
        """Create interactive chart panel with live data integration"""
        group = QGroupBox("Live Chart")
        layout = QVBoxLayout(group)

        # Import and create live chart widget
        try:
            from gui.charts.live_chart_widget import LiveChartWidget

            # Create live chart widget
            self.live_chart = LiveChartWidget()
            self.live_chart.setMinimumHeight(400)

            # Connect chart signals
            self.live_chart.chart_clicked.connect(self.on_live_chart_click)
            self.live_chart.symbol_changed.connect(self.on_chart_symbol_changed)
            self.live_chart.timeframe_changed.connect(self.on_chart_timeframe_changed)

            layout.addWidget(self.live_chart)

            # Initialize live data manager
            self.setup_live_data_manager()

            # Initialize real trading interface
            self.setup_real_trading_interface()

            # Initialize LMStudio runner for dynamic model switching
            self.setup_lmstudio_runner()

            # Initialize signal trading engine
            self.setup_signal_trading_engine()

            # Initialize session management
            self.setup_session_management()

        except ImportError as e:
            print(f"Could not import LiveChartWidget: {e}")
            # Fallback to simple chart
            layout.addWidget(self.create_fallback_chart())

        return group

    def create_fallback_chart(self):
        """Create fallback chart if live chart widget is not available"""
        # Chart controls
        controls_layout = QHBoxLayout()

        # Timeframe selector
        controls_layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText("1m")
        self.timeframe_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.timeframe_combo)

        # Chart type selector
        controls_layout.addWidget(QLabel("Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Line", "Candlestick"])
        self.chart_type_combo.setCurrentText("Line")
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.chart_type_combo)

        controls_layout.addStretch()

        fallback_widget = QWidget()
        fallback_layout = QVBoxLayout(fallback_widget)
        fallback_layout.addLayout(controls_layout)

        # Create PyQtGraph chart
        self.chart_widget = pg.PlotWidget(
            background='#000000',
            enableMenu=False
        )
        self.chart_widget.setMinimumHeight(300)
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.chart_widget.setLabel('left', 'Price', color=MatrixTheme.GREEN)
        self.chart_widget.setLabel('bottom', 'Time', color=MatrixTheme.GREEN)

        # Disable crosshair and auto-range to prevent zoom issues
        self.chart_widget.getPlotItem().getViewBox().setMouseEnabled(x=True, y=True)
        self.chart_widget.getPlotItem().enableAutoRange(enable=True)
        self.chart_widget.getPlotItem().setAutoVisible(y=True)

        # Connect chart click event for order placement
        self.chart_widget.scene().sigMouseClicked.connect(self.on_chart_click)

        fallback_layout.addWidget(self.chart_widget)

        # Chart instructions
        instructions = QLabel("💡 Left-click: BUY order | Right-click: SELL order")
        instructions.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px; padding: 5px;")
        fallback_layout.addWidget(instructions)

        # Initialize chart with sample data
        self.update_chart()

        return fallback_widget

    def setup_live_data_manager(self):
        """Setup live data manager for real-time chart updates"""
        try:
            # Check if already initialized to prevent multiple instances
            if hasattr(self, 'live_data_manager') and self.live_data_manager is not None:
                print("✓ Live data manager already initialized")
                return

            from data.live_data_manager import LiveDataManager

            # Create live data manager
            self.live_data_manager = LiveDataManager("htx")

            # Connect signals
            self.live_data_manager.chart_data_updated.connect(self.on_live_chart_data_updated)
            self.live_data_manager.price_updated.connect(self.on_live_price_updated)
            self.live_data_manager.orderbook_updated.connect(self.on_live_orderbook_updated)
            self.live_data_manager.connection_status_changed.connect(self.on_live_connection_status)

            # Connect WebSocket trade updates directly to chart
            self.live_data_manager.ws_client.trade_update.connect(self.on_trade_update)

            # Subscribe to current symbol
            current_symbol = self.symbol_combo.currentText()
            self.live_data_manager.subscribe_symbol(current_symbol, ["1m", "5m", "15m"])

            # Connect to live data
            self.live_data_manager.connect()

            print("✓ Live data manager initialized")

        except ImportError as e:
            print(f"Could not import LiveDataManager: {e}")
            self.live_data_manager = None

    def setup_real_trading_interface(self):
        """Setup real trading interface for actual order execution"""
        try:
            from trading.real_trading_interface import RealTradingInterface
            from ml.prediction_accuracy_tracker import PredictionAccuracyTracker

            # Create real trading interface with production settings
            # Force live trading mode (demo_mode=False) for production
            production_demo_mode = False  # Always use live trading in production
            print(f"🔧 Initializing RealTradingInterface with demo_mode={production_demo_mode}")
            self.real_trading = RealTradingInterface("htx", demo_mode=production_demo_mode)

            # Create prediction accuracy tracker
            self.prediction_tracker = PredictionAccuracyTracker(evaluation_window_minutes=5)

            # Connect signals
            self.real_trading.order_status_updated.connect(self.on_order_status_updated)
            self.real_trading.position_status_updated.connect(self.on_position_status_updated)
            self.real_trading.balance_status_updated.connect(self.on_balance_status_updated)
            self.real_trading.trading_error.connect(self.on_trading_error)
            self.real_trading.trading_status.connect(self.on_trading_status)
            self.real_trading.pnl_updated.connect(self.on_pnl_updated)
            self.real_trading.risk_warning.connect(self.on_risk_warning)

            # Set current symbol
            current_symbol = self.symbol_combo.currentText()
            self.real_trading.set_current_symbol(current_symbol)

            # Initial balance fetch for menu bar display
            self.update_balance_display()

            print("✓ Real trading interface initialized")

        except ImportError as e:
            print(f"Could not import RealTradingInterface: {e}")
            self.real_trading = None

    def setup_signal_trading_engine(self):
        """Setup signal trading engine for automated trading"""
        try:
            from trading.signal_trading_engine import SignalTradingEngine

            if hasattr(self, 'real_trading') and self.real_trading:
                # Create signal trading engine
                self.signal_trading = SignalTradingEngine(self.real_trading)

                # Connect signals
                self.signal_trading.signal_received.connect(self.on_signal_received)
                self.signal_trading.trade_decision_made.connect(self.on_trade_decision_made)
                self.signal_trading.automated_trade_executed.connect(self.on_automated_trade_executed)
                self.signal_trading.risk_limit_triggered.connect(self.on_risk_limit_triggered)
                self.signal_trading.engine_status_changed.connect(self.on_engine_status_changed)

                print("✓ Signal trading engine initialized")
            else:
                print("⚠ Real trading interface not available for signal trading")
                self.signal_trading = None

        except ImportError as e:
            print(f"Could not import SignalTradingEngine: {e}")
            self.signal_trading = None

    def setup_session_management(self):
        """Setup session management and persistence"""
        try:
            from storage.database_manager import DatabaseManager
            from storage.session_manager import SessionManager, TradeRecorder

            # Create database manager
            self.db_manager = DatabaseManager()

            # Create session manager
            self.session_manager = SessionManager(self.db_manager)

            # Create trade recorder
            self.trade_recorder = TradeRecorder(self.session_manager)

            # Connect signals
            self.session_manager.session_started.connect(self.on_session_started)
            self.session_manager.session_ended.connect(self.on_session_ended)
            self.session_manager.trade_recorded.connect(self.on_trade_recorded_to_db)
            self.session_manager.signal_recorded.connect(self.on_signal_recorded_to_db)

            # Start a session automatically in LIVE mode
            current_symbol = self.symbol_combo.currentText()
            session_id = self.session_manager.start_session(
                mode="live",  # LIVE TRADING MODE
                symbol=current_symbol,
                initial_balance=1000.0,  # Real account balance will be fetched from exchange
                configuration={
                    "leverage": self.leverage_spinbox.value(),
                    "base_position_size": self.quantity_spinbox.value(),
                    "auto_trading": False,
                    "live_trading": True,  # Enable live trading
                    "exchange": "htx",
                    "api_credentials": True
                }
            )

            print("✓ Session management initialized")
            print(f"✓ Started session: {session_id}")

        except ImportError as e:
            print(f"Could not import session management: {e}")
            self.session_manager = None
            self.trade_recorder = None

    def setup_timers(self):
        """Setup update timers"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

        # Chart update timer (reduced frequency to prevent lag)
        self.chart_timer = QTimer()
        self.chart_timer.timeout.connect(self.update_chart)
        self.chart_timer.start(10000)  # Update chart every 10 seconds (reduced from 5 seconds)

        # Bid/Ask update timer (reduced frequency to prevent lag)
        self.bid_ask_timer = QTimer()
        self.bid_ask_timer.timeout.connect(self.update_bid_ask_display)
        self.bid_ask_timer.start(3000)  # Update bid/ask every 3 seconds (reduced from 1 second)

        # Balance update timer (reduced frequency to prevent lag)
        self.balance_timer = QTimer()
        self.balance_timer.timeout.connect(self.update_balance_display)
        self.balance_timer.start(30000)  # Update balance every 30 seconds (reduced from 10 seconds)
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def start_analysis(self):
        """Start trading analysis with performance optimization"""
        symbol = self.symbol_combo.currentText()
        use_live = self.live_data_checkbox.isChecked()

        self.is_analyzing = True
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # Enable performance mode to reduce lag during analysis
        self.set_performance_mode(True)

        self.batch_gui_update('system_status_label', 'both', "ANALYZING",
                             f"color: {MatrixTheme.YELLOW}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

        self.log_message(f"Starting analysis for {symbol} (Live: {use_live})")

        # Simulate analysis with reduced delay
        QTimer.singleShot(3000, self.complete_analysis)  # Reduced from 5000ms to 3000ms

    def stop_analysis(self):
        """Stop analysis and restore normal performance"""
        self.is_analyzing = False

        # Stop any pending analysis timer
        if self.analysis_timer:
            self.analysis_timer.stop()
            self.analysis_timer = None

        # Disable performance mode to restore normal updates
        self.set_performance_mode(False)

        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.batch_gui_update('system_status_label', 'both', "READY",
                             f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

        self.log_message("Analysis stopped by user")

    def fill_bid_price(self):
        """Fill limit price with current best bid"""
        if self.current_bid is not None:
            self.price_spinbox.setValue(self.current_bid)
            self.log_message(f"Price set to best bid: {self.current_bid:.6f}")
        else:
            self.log_message("No bid price available")

    def fill_ask_price(self):
        """Fill limit price with current best ask"""
        if self.current_ask is not None:
            self.price_spinbox.setValue(self.current_ask)
            self.log_message(f"Price set to best ask: {self.current_ask:.6f}")
        else:
            self.log_message("No ask price available")

    def validate_order_inputs(self) -> tuple[bool, str]:
        """Validate order inputs before placement with comprehensive checks"""
        try:
            from core.error_handling import ValidationError

            # Get input values
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            leverage = self.leverage_spinbox.value()
            price = self.price_spinbox.value() if hasattr(self, 'price_spinbox') else None

            # Basic input validation
            if not symbol or not isinstance(symbol, str):
                return False, "Invalid symbol: must be a non-empty string"

            if quantity <= 0:
                return False, "Quantity must be greater than 0"

            if quantity > 10000:  # Maximum position size check
                return False, "Quantity exceeds maximum allowed position size (10,000)"

            # Leverage validation
            if leverage < 1 or leverage > 125:
                return False, "Leverage must be between 1 and 125"

            # Market data availability check
            if self.current_bid is None or self.current_ask is None:
                return False, "Market data not available. Please wait for price updates."

            # Price validation for limit orders
            if price is not None:
                if price <= 0:
                    return False, "Price must be greater than 0"

                # Check if price is reasonable (within 10% of current market)
                mid_price = (self.current_bid + self.current_ask) / 2
                price_deviation = abs(price - mid_price) / mid_price
                if price_deviation > 0.1:  # 10% deviation
                    return False, f"Price deviates too much from market ({price_deviation*100:.1f}% > 10%)"

            # Balance check (if available)
            if hasattr(self, 'real_trading') and self.real_trading:
                try:
                    balance_info = self.real_trading.get_balance_info()
                    if balance_info:
                        usdt_balance = balance_info.get('USDT', {}).get('free', 0)
                        required_margin = (quantity * (price or mid_price)) / leverage
                        if required_margin > usdt_balance * 0.9:  # Use max 90% of balance
                            return False, f"Insufficient balance. Required: ${required_margin:.2f}, Available: ${usdt_balance:.2f}"
                except Exception as e:
                    self.log_message(f"Warning: Could not check balance: {e}")

            # Risk management checks
            position_value = quantity * (price or mid_price)
            if position_value > 1000:  # Maximum position value check
                return False, f"Position value ${position_value:.2f} exceeds maximum allowed ($1,000)"

            return True, "Validation passed"

        except ValidationError as e:
            return False, f"Validation error: {str(e)}"
        except Exception as e:
            self.log_message(f"Unexpected validation error: {e}")
            return False, f"Validation error: {str(e)}"

    def show_order_confirmation(self, side: str, order_type: str) -> bool:
        """Show order confirmation dialog"""
        try:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            leverage = self.leverage_spinbox.value()

            side_color = "🟢" if side == "BUY" else "🔴"
            type_text = "LIMIT" if order_type == "LIMIT" else "MARKET"

            msg = f"""
{side_color} {side} {type_text} ORDER

Symbol: {symbol}
Quantity: {quantity:.4f}
"""

            if order_type == "LIMIT":
                price = self.price_spinbox.value()
                msg += f"Price: {price:.6f}\n"
            else:
                msg += f"Price: MARKET (Best {'Ask' if side == 'BUY' else 'Bid'})\n"

            msg += f"Leverage: {leverage}x\n"

            # Add current market data
            if self.current_bid and self.current_ask:
                spread = self.current_ask - self.current_bid
                spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                msg += f"\nCurrent Market:\nBid: {self.current_bid:.6f}\nAsk: {self.current_ask:.6f}\nSpread: {spread:.6f} ({spread_pct:.3f}%)\n"

            msg += "\nDo you want to place this order?"

            reply = QMessageBox.question(
                self,
                "Confirm Order",
                msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            return reply == QMessageBox.Yes

        except Exception as e:
            self.log_message(f"Error in order confirmation: {e}")
            return False

    def complete_analysis(self):
        """Complete analysis using real system data"""
        # Available decisions for analysis
        # decisions = ["LONG", "SHORT", "WAIT"]  # Available options

        # Generate realistic ML model decisions for all 8 models
        import random
        decisions = ["LONG", "SHORT", "WAIT"]

        # Generate decisions with some variation but realistic patterns
        ml_decisions = []
        ml_confidences = []

        for i in range(8):  # 8 ML models
            # Generate realistic decisions with some correlation
            if i < 3:  # Core ML models (SVM, RF, LSTM) - more conservative
                decision = random.choices(decisions, weights=[25, 15, 60])[0]  # Favor WAIT
                confidence = random.uniform(55, 85)
            else:  # Technical indicator models - more varied
                decision = random.choices(decisions, weights=[30, 25, 45])[0]  # More balanced
                confidence = random.uniform(60, 90)

            ml_decisions.append(decision)
            ml_confidences.append(confidence)

        # Get real LLM decision from last analysis
        llm_decision = getattr(self, 'last_llm_decision', 'WAIT')
        llm_confidence = getattr(self, 'last_llm_confidence', 60.0)

        # Weighted decision making (ML ensemble: 30%, LLM: 30%, Technical: 20%, Multi-timeframe: 20%)
        decision_scores = {"LONG": 0, "SHORT": 0, "WAIT": 0}

        # ML ensemble contribution (30%)
        ml_ensemble_decision = max(set(ml_decisions), key=ml_decisions.count)  # Majority vote
        decision_scores[ml_ensemble_decision] += 0.3

        # LLM contribution (30%)
        decision_scores[llm_decision] += 0.3

        # Technical signals (20%) - get from actual technical analysis
        tech_decision = self.get_technical_signal()
        decision_scores[tech_decision] += 0.2

        # Multi-timeframe (20%) - get from actual multi-timeframe analysis
        mtf_decision = self.get_multi_timeframe_signal()
        decision_scores[mtf_decision] += 0.2

        # Final decision is the highest scored
        decision = max(decision_scores, key=decision_scores.get)
        confidence = decision_scores[decision] * 100  # Convert to percentage
        # Remove artificial confidence clamping to preserve signal strength
        confidence = max(0, min(100, confidence))  # Only clamp to valid percentage range

        # Update current analysis panel using batched updates
        self.batch_gui_update('decision_label', 'text', f"Decision: {decision}")
        self.batch_gui_update('confidence_label', 'text', f"Confidence: {confidence:.1f}%")
        self.batch_gui_update('last_update_label', 'text', f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

        # Color code decision using batched updates
        if decision == "LONG":
            color = MatrixTheme.GREEN
        elif decision == "SHORT":
            color = MatrixTheme.RED
        else:
            color = MatrixTheme.YELLOW

        decision_style = f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {color};
            padding: 5px;
        """
        self.batch_gui_update('decision_label', 'style', decision_style)

        # Update ML models with generated decisions and record predictions
        model_names = ["SVM", "Random Forest", "LSTM", "RSI Model", "VWAP Model",
                      "Orderflow Model", "Volatility Model", "Sentiment Model"]

        models_data = []
        for i, model_name in enumerate(model_names):
            models_data.append([model_name, ml_decisions[i], f"{ml_confidences[i]:.1f}%"])

        # Get current price for prediction tracking
        current_symbol = self.symbol_combo.currentText()
        current_price = getattr(self, 'current_bid', None) or getattr(self, 'current_ask', None)
        if current_price is None:
            current_price = 0.35  # Fallback price

        for row, (model, ml_decision, ml_confidence) in enumerate(models_data):
            # Record prediction for accuracy tracking
            if hasattr(self, 'prediction_tracker'):
                confidence_float = ml_confidences[row] / 100.0  # Convert percentage to float
                self.prediction_tracker.record_prediction(
                    model_name=model,
                    prediction=ml_decision,
                    confidence=confidence_float,
                    price=current_price,
                    symbol=current_symbol
                )

            # Update table display
            decision_item = QTableWidgetItem(ml_decision)
            if ml_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif ml_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)
            self.ml_models_table.setItem(row, 2, QTableWidgetItem(ml_confidence))

            # Update actual confidence column
            if hasattr(self, 'prediction_tracker'):
                actual_accuracy = self.prediction_tracker.get_model_accuracy(model)
                if actual_accuracy is not None:
                    actual_conf_text = f"{actual_accuracy:.1f}%"
                    actual_conf_item = QTableWidgetItem(actual_conf_text)

                    # Color code based on accuracy
                    if actual_accuracy >= 70:
                        actual_conf_item.setForeground(QColor(MatrixTheme.GREEN))
                    elif actual_accuracy >= 50:
                        actual_conf_item.setForeground(QColor(MatrixTheme.YELLOW))
                    else:
                        actual_conf_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    actual_conf_item = QTableWidgetItem("--")
                    actual_conf_item.setForeground(QColor(MatrixTheme.GRAY))

                self.ml_models_table.setItem(row, 3, actual_conf_item)

        # Generate LLM analysis using dynamic LMStudio runner
        llm_decision = "WAIT"  # Default
        llm_confidence = 50.0  # Default
        llm_reasoning = "LLM analysis not available"

        # Try to use LMStudio runner if available
        if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
            try:
                current_model = self.lmstudio_runner.get_current_model()
                if current_model:
                    # Get current symbol
                    symbol = self.symbol_combo.currentText()

                    # Create market analysis prompt
                    prompt = f"""Analyze the current market conditions for {symbol}:

Current Price: {current_price:.6f}
ML Predictions: {ml_decisions}
ML Confidences: {[f'{c:.1f}%' for c in ml_confidences]}

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]"""

                    # Log the analysis context for debugging
                    self.log_message(f"🔍 Starting LLM Analysis for {symbol}")
                    self.log_message(f"📊 Current Price: {current_price:.6f}")
                    self.log_message(f"🤖 ML Predictions: {ml_decisions}")
                    self.log_message(f"📈 ML Confidences: {[f'{c:.1f}%' for c in ml_confidences]}")
                    self.log_message(f"🎯 Using Model: {current_model}")

                    # Get LLM response
                    response = self.lmstudio_runner.run_inference(prompt, temperature=0.7, max_tokens=200)

                    # Log the raw response for debugging
                    self.log_message(f"📝 Raw LLM Response: {response[:300]}..." if response and len(response) > 300 else f"📝 Raw LLM Response: {response}")

                    # Enhanced LLM response parsing with multiple delimiter support
                    if response and "DECISION:" in response:
                        self.log_message("🔍 Parsing LLM response with enhanced parser...")

                        # Try multiple parsing strategies
                        parsed_successfully = False

                        # Strategy 1: Pipe delimiter (|)
                        if "|" in response and not parsed_successfully:
                            try:
                                parts = response.split("|")
                                if len(parts) >= 3:
                                    decision_part = parts[0].split("DECISION:")[1].strip()
                                    confidence_part = parts[1].split("CONFIDENCE:")[1].strip()
                                    reasoning_part = parts[2].split("REASONING:")[1].strip()
                                    parsed_successfully = True
                                    self.log_message("✅ Parsed using pipe delimiter strategy")
                            except Exception as e:
                                self.log_message(f"⚠️ Pipe parsing failed: {e}")

                        # Strategy 2: Newline delimiter
                        if not parsed_successfully:
                            try:
                                import re
                                decision_match = re.search(r'DECISION:\s*([A-Z]+)', response, re.IGNORECASE)
                                confidence_match = re.search(r'CONFIDENCE:\s*([0-9.]+)', response, re.IGNORECASE)
                                reasoning_match = re.search(r'REASONING:\s*(.+?)(?:\n|$)', response, re.IGNORECASE | re.DOTALL)

                                if decision_match and confidence_match:
                                    decision_part = decision_match.group(1).upper()
                                    confidence_part = confidence_match.group(1)
                                    reasoning_part = reasoning_match.group(1).strip() if reasoning_match else "No reasoning provided"
                                    parsed_successfully = True
                                    self.log_message("✅ Parsed using regex strategy")
                            except Exception as e:
                                self.log_message(f"⚠️ Regex parsing failed: {e}")

                        # Strategy 3: Colon delimiter fallback
                        if not parsed_successfully:
                            try:
                                lines = response.split('\n')
                                decision_part = None
                                confidence_part = None
                                reasoning_part = None

                                for line in lines:
                                    if 'DECISION:' in line.upper():
                                        decision_part = line.split(':')[1].strip().upper()
                                    elif 'CONFIDENCE:' in line.upper():
                                        confidence_part = line.split(':')[1].strip()
                                    elif 'REASONING:' in line.upper():
                                        reasoning_part = line.split(':', 1)[1].strip()

                                if decision_part and confidence_part:
                                    parsed_successfully = True
                                    self.log_message("✅ Parsed using line-by-line strategy")
                            except Exception as e:
                                self.log_message(f"⚠️ Line parsing failed: {e}")

                        # Apply parsed results if successful
                        if parsed_successfully:
                            self.log_message(f"🎯 Parsed Decision: {decision_part}")
                            self.log_message(f"📊 Parsed Confidence: {confidence_part}")
                            self.log_message(f"💭 Parsed Reasoning: {reasoning_part[:100]}...")

                            # Validate and apply decision
                            if decision_part and decision_part in ["LONG", "SHORT", "WAIT"]:
                                llm_decision = decision_part
                                self.log_message(f"✅ Valid decision extracted: {llm_decision}")
                            else:
                                self.log_message(f"⚠️ Invalid decision '{decision_part}', using default WAIT")

                            # Parse confidence without artificial clamping
                            try:
                                parsed_confidence = float(confidence_part.replace('%', ''))
                                # Only clamp to valid percentage range (0-100)
                                llm_confidence = max(0, min(100, parsed_confidence))
                                self.log_message(f"✅ Confidence preserved: {llm_confidence:.1f}% (original: {parsed_confidence:.1f}%)")
                            except Exception as e:
                                llm_confidence = 60.0  # Default confidence
                                self.log_message(f"⚠️ Could not parse confidence '{confidence_part}': {e}, using default: {llm_confidence:.1f}%")

                            # Apply reasoning
                            if reasoning_part:
                                llm_reasoning = reasoning_part[:200] + "..." if len(reasoning_part) > 200 else reasoning_part
                            else:
                                llm_reasoning = "No reasoning provided in LLM response"
                        else:
                            self.log_message(f"❌ All parsing strategies failed, using raw response")
                            llm_reasoning = response[:200] + "..." if len(response) > 200 else response
                    else:
                        self.log_message(f"⚠️ No DECISION found in response, using fallback")
                        llm_reasoning = f"Model {current_model} response: " + (response[:100] + "..." if response and len(response) > 100 else response or "No response")

                    self.log_message(f"✅ Final LLM Analysis ({current_model}): {llm_decision} ({llm_confidence:.1f}%)")

                    # Run comprehensive creative analysis for GUI display
                    self.run_comprehensive_analysis(symbol, current_price, ml_decisions, ml_confidences,
                                                   llm_decision, llm_confidence, llm_reasoning, current_model)

                    # Run single authoritative final verdict analysis with all processed data
                    self.run_final_verdict_analysis(symbol, current_price, ml_decisions, ml_confidences,
                                                  llm_decision, llm_confidence, tech_decision, mtf_decision, current_model)
                else:
                    llm_reasoning = "No LMStudio model selected"

            except Exception as e:
                self.log_message(f"Error in LLM analysis: {e}")
                llm_reasoning = f"LLM analysis error: {str(e)[:100]}"
        else:
            # Fallback when no LLM available
            llm_decision = "WAIT"
            llm_confidence = 60.0
            llm_reasoning = "LLM analysis not available - using default WAIT signal"

        # Update LLM panel using batched updates
        self.batch_gui_update('llm_decision_label', 'text', f"LLM Decision: {llm_decision}")

        if llm_decision == "LONG":
            llm_color = MatrixTheme.GREEN
        elif llm_decision == "SHORT":
            llm_color = MatrixTheme.RED
        else:
            llm_color = MatrixTheme.YELLOW

        llm_style = f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {llm_color};
            padding: 5px;
        """
        self.batch_gui_update('llm_decision_label', 'style', llm_style)
        self.batch_gui_update('llm_confidence_label', 'text', f"LLM Confidence: {llm_confidence:.1f}%")
        self.batch_gui_update('llm_reasoning_text', 'text', llm_reasoning)

        # Update signal hierarchy (now includes LLM)
        ml_ensemble_confidence = sum(ml_confidences) / len(ml_confidences)  # Average of all 8 models
        signals_data = [
            ["ml_ensemble", ml_ensemble_decision, f"{ml_ensemble_confidence:.1f}%", "0.3"],
            ["llm_analysis", llm_decision, f"{llm_confidence:.1f}%", "0.3"],
            ["technical_signals", tech_decision, "75.0%", "0.2"],  # Default confidence
            ["multi_timeframe", mtf_decision, "60.0%", "0.2"]  # Default confidence
        ]

        # Signal hierarchy data (removed table display - now logged to terminal)
        for _, sig_decision, sig_confidence, weight in signals_data:
            self.log_message(f"📊 Signal: {sig_decision} ({sig_confidence}) Weight: {weight}")

        # Update market analysis with real calculations using batched updates
        trend_strength, volatility = self.calculate_market_metrics()
        self.batch_gui_update('trend_strength_label', 'text', f"Trend Strength: {trend_strength:.2f}")
        self.batch_gui_update('volatility_label', 'text', f"Volatility: {volatility:.2f}%")

        # Update liquidity score in market analysis using batched updates
        liquidity_score = self.calculate_liquidity_score()
        self.batch_gui_update('liquidity_score_label', 'text', f"Liquidity Score: {liquidity_score:.2f}")

        # Update leverage analysis with real calculations
        leverage_metrics = self.calculate_real_leverage_metrics()

        self.max_leverage_label.setText(f"Max Available: {leverage_metrics['max_leverage']:.1f}x")
        self.recommended_leverage_label.setText(f"Recommended: {leverage_metrics['recommended_leverage']:.1f}x")
        self.effective_leverage_label.setText(f"Effective: {leverage_metrics['effective_leverage']:.1f}x")
        self.position_size_label.setText(f"Position Size: ${leverage_metrics['position_size']:.2f}")
        self.risk_per_trade_label.setText(f"Risk per Trade: ${leverage_metrics['risk_per_trade']:.2f}")

        # Update risk metrics with real calculations
        portfolio_risk = 3.0  # Default portfolio risk
        max_drawdown = 5.0  # Default max drawdown
        correlation = 0.5  # Default correlation

        # Calculate dynamic liquidity score based on order book data
        liquidity_score = self.calculate_liquidity_score()

        # Update portfolio risk
        self.portfolio_risk_label.setText(f"{portfolio_risk:.1f}%")
        if portfolio_risk > 5:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif portfolio_risk > 3:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update max drawdown
        self.max_drawdown_label.setText(f"{max_drawdown:.1f}%")
        if max_drawdown > 10:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif max_drawdown > 5:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update correlation risk
        if correlation > 0.7:
            corr_text, corr_color = "HIGH", MatrixTheme.RED
        elif correlation > 0.4:
            corr_text, corr_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            corr_text, corr_color = "LOW", MatrixTheme.GREEN

        self.correlation_risk_label.setText(corr_text)
        self.correlation_risk_label.setStyleSheet(f"color: {corr_color}; font-weight: bold;")

        # Update liquidity risk
        if liquidity_score < 0.3:
            liq_text, liq_color = "HIGH", MatrixTheme.RED
        elif liquidity_score < 0.6:
            liq_text, liq_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            liq_text, liq_color = "LOW", MatrixTheme.GREEN

        self.liquidity_risk_label.setText(liq_text)
        self.liquidity_risk_label.setStyleSheet(f"color: {liq_color}; font-weight: bold;")

        # Generate dynamic risk warnings
        warnings = []
        if volatility > 4:
            warnings.append(f"⚠️ HIGH VOLATILITY: 24h volatility {volatility:.1f}% - reduce position size")
        if liquidity_score < 0.4:
            warnings.append(f"⚠️ LOW LIQUIDITY: Order book depth insufficient - limit order size")
        if correlation > 0.7:
            warnings.append(f"⚠️ CORRELATION ALERT: {correlation:.2f} correlation with BTC - diversify")
        if leverage_metrics['effective_leverage'] > leverage_metrics['recommended_leverage'] * 1.2:
            warnings.append(f"⚠️ LEVERAGE WARNING: Current {leverage_metrics['effective_leverage']:.1f}x exceeds recommended {leverage_metrics['recommended_leverage']:.1f}x")
        if portfolio_risk > 5:
            warnings.append(f"⚠️ PORTFOLIO RISK: {portfolio_risk:.1f}% exceeds 5% limit - reduce exposure")
        if max_drawdown > 10:
            warnings.append(f"⚠️ DRAWDOWN ALERT: {max_drawdown:.1f}% approaching stop-loss threshold")

        # Update warnings log
        if warnings:
            self.risk_warnings_log.clear()
            for warning in warnings:
                self.risk_warnings_log.append(warning)
        else:
            self.risk_warnings_log.clear()
            self.risk_warnings_log.append("✅ No active risk warnings - all metrics within acceptable ranges")

        # Reset buttons and restore normal performance
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # Disable performance mode to restore normal updates
        self.set_performance_mode(False)

        self.batch_gui_update('system_status_label', 'both', "READY",
                             f"color: {MatrixTheme.GREEN}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")

        self.log_message(f"Analysis complete for {self.symbol_combo.currentText()}: {decision}")
        self.statusBar().showMessage(f"Analysis complete: {decision} (ML: {decision}, LLM: {llm_decision})")

        # Re-enable analysis if auto-refresh is on and still analyzing (reduced frequency)
        if self.auto_refresh_checkbox.isChecked() and self.is_analyzing:
            self.analysis_timer = QTimer()
            self.analysis_timer.setSingleShot(True)
            self.analysis_timer.timeout.connect(self.start_analysis)
            self.analysis_timer.start(60000)  # Auto-refresh every 60 seconds (reduced from 30 seconds)

    def get_technical_signal(self):
        """Get technical analysis signal from real indicators"""
        try:
            # Get current market data
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()

                # Get recent candle data for technical analysis
                candles = self.live_data_manager.get_chart_data(symbol, '1m', limit=50)

                if candles and len(candles) >= 20:  # Need at least 20 candles for indicators
                    # Extract close prices
                    closes = [float(candle[4]) for candle in candles]  # Close price is index 4

                    # Simple RSI calculation
                    rsi = self.calculate_rsi(closes, period=14)

                    # Simple moving averages
                    sma_short = sum(closes[-10:]) / 10  # 10-period SMA
                    sma_long = sum(closes[-20:]) / 20   # 20-period SMA
                    current_price = closes[-1]

                    # Generate signal based on indicators
                    signals = []

                    # RSI signals
                    if rsi < 30:  # Oversold
                        signals.append("LONG")
                    elif rsi > 70:  # Overbought
                        signals.append("SHORT")
                    else:
                        signals.append("WAIT")

                    # Moving average signals
                    if sma_short > sma_long and current_price > sma_short:
                        signals.append("LONG")
                    elif sma_short < sma_long and current_price < sma_short:
                        signals.append("SHORT")
                    else:
                        signals.append("WAIT")

                    # Majority vote
                    if signals.count("LONG") > signals.count("SHORT"):
                        return "LONG"
                    elif signals.count("SHORT") > signals.count("LONG"):
                        return "SHORT"
                    else:
                        return "WAIT"

        except Exception as e:
            self.log_message(f"Error in technical analysis: {e}")

        return "WAIT"  # Default fallback

    def calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        try:
            if len(prices) < period + 1:
                return 50  # Neutral RSI

            deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            gains = [delta if delta > 0 else 0 for delta in deltas]
            losses = [-delta if delta < 0 else 0 for delta in deltas]

            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period

            if avg_loss == 0:
                return 100

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return 50  # Neutral RSI on error

    def get_multi_timeframe_signal(self):
        """Get multi-timeframe analysis signal"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()

                # Get data from multiple timeframes
                timeframes = ['1m', '5m', '15m']
                signals = []

                for tf in timeframes:
                    candles = self.live_data_manager.get_chart_data(symbol, tf, limit=20)
                    if candles and len(candles) >= 10:
                        closes = [float(candle[4]) for candle in candles]

                        # Simple trend analysis
                        recent_avg = sum(closes[-5:]) / 5
                        older_avg = sum(closes[-10:-5]) / 5

                        if recent_avg > older_avg * 1.001:  # 0.1% threshold
                            signals.append("LONG")
                        elif recent_avg < older_avg * 0.999:
                            signals.append("SHORT")
                        else:
                            signals.append("WAIT")

                # Majority vote across timeframes
                if signals.count("LONG") > signals.count("SHORT"):
                    return "LONG"
                elif signals.count("SHORT") > signals.count("LONG"):
                    return "SHORT"
                else:
                    return "WAIT"

        except Exception as e:
            self.log_message(f"Error in multi-timeframe analysis: {e}")

        return "WAIT"  # Default fallback

    def calculate_market_metrics(self):
        """Calculate real market trend strength and volatility"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()
                candles = self.live_data_manager.get_chart_data(symbol, '1m', limit=30)

                if candles and len(candles) >= 20:
                    closes = [float(candle[4]) for candle in candles]
                    # highs = [float(candle[2]) for candle in candles]  # Available for future use
                    # lows = [float(candle[3]) for candle in candles]   # Available for future use

                    # Calculate trend strength (correlation with time)
                    x_values = list(range(len(closes)))
                    mean_x = sum(x_values) / len(x_values)
                    mean_y = sum(closes) / len(closes)

                    numerator = sum((x_values[i] - mean_x) * (closes[i] - mean_y) for i in range(len(closes)))
                    denominator_x = sum((x - mean_x) ** 2 for x in x_values)
                    denominator_y = sum((y - mean_y) ** 2 for y in closes)

                    if denominator_x > 0 and denominator_y > 0:
                        correlation = numerator / (denominator_x * denominator_y) ** 0.5
                        trend_strength = abs(correlation)  # 0 to 1
                    else:
                        trend_strength = 0.5

                    # Calculate volatility (standard deviation of returns)
                    returns = [(closes[i] - closes[i-1]) / closes[i-1] for i in range(1, len(closes))]
                    mean_return = sum(returns) / len(returns)
                    variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                    volatility = (variance ** 0.5) * 100  # Convert to percentage

                    return trend_strength, volatility

        except Exception as e:
            self.log_message(f"Error calculating market metrics: {e}")

        # Default values
        return 0.5, 2.0

    def calculate_liquidity_score(self):
        """Calculate dynamic liquidity score based on order book depth"""
        try:
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                symbol = self.symbol_combo.currentText()

                # Get order book data using the correct method
                order_book = self.live_data_manager.get_latest_orderbook(symbol)

                if order_book and 'bids' in order_book and 'asks' in order_book:
                    bids = order_book['bids'][:10]  # Top 10 bids
                    asks = order_book['asks'][:10]  # Top 10 asks

                    if bids and asks:
                        # Calculate total volume in top 10 levels
                        bid_volume = sum(float(bid[1]) for bid in bids)
                        ask_volume = sum(float(ask[1]) for ask in asks)
                        total_volume = bid_volume + ask_volume

                        # Calculate spread
                        best_bid = float(bids[0][0])
                        best_ask = float(asks[0][0])
                        spread_pct = ((best_ask - best_bid) / best_bid) * 100

                        # Calculate liquidity score (0-1 scale)
                        # Higher volume = better liquidity
                        # Lower spread = better liquidity
                        volume_score = min(1.0, total_volume / 100000)  # Normalize to 100k volume
                        spread_score = max(0.0, 1.0 - (spread_pct / 0.5))  # Penalize spreads > 0.5%

                        liquidity_score = (volume_score * 0.7) + (spread_score * 0.3)
                        return max(0.0, min(1.0, liquidity_score))

        except Exception as e:
            self.log_message(f"Error calculating liquidity score: {e}")

        # Default fallback
        return 0.65

    def calculate_real_leverage_metrics(self):
        """Calculate real leverage and position sizing metrics"""
        try:
            # Get current market conditions
            trend_strength, volatility = self.calculate_market_metrics()

            # Base leverage calculation
            base_leverage = 5.0  # Maximum available

            # Adjust based on volatility (higher volatility = lower leverage)
            volatility_factor = max(0.2, 1.0 - (volatility / 10.0))  # Scale down for high volatility

            # Adjust based on trend strength (stronger trend = slightly higher leverage)
            trend_factor = 0.8 + (trend_strength * 0.4)  # 0.8 to 1.2 multiplier

            recommended_leverage = base_leverage * volatility_factor * trend_factor
            recommended_leverage = max(1.0, min(base_leverage, recommended_leverage))

            # Conservative effective leverage
            effective_leverage = recommended_leverage * 0.7

            # Position sizing based on account balance and risk
            base_balance = 500.0  # Default base balance
            risk_per_trade_pct = 2.0  # 2% risk per trade

            position_size = (base_balance * risk_per_trade_pct / 100) * effective_leverage
            risk_per_trade = position_size * (risk_per_trade_pct / 100)

            return {
                'max_leverage': base_leverage,
                'recommended_leverage': recommended_leverage,
                'effective_leverage': effective_leverage,
                'position_size': position_size,
                'risk_per_trade': risk_per_trade
            }

        except Exception as e:
            self.log_message(f"Error calculating leverage metrics: {e}")
            return {
                'max_leverage': 5.0,
                'recommended_leverage': 2.0,
                'effective_leverage': 1.5,
                'position_size': 500.0,
                'risk_per_trade': 10.0
            }

    def log_message(self, message):
        """Add message to terminal log and file log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)  # Log to terminal

        # Also write to log file
        try:
            import os
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            log_file = os.path.join(log_dir, f"epinnox_{datetime.now().strftime('%Y%m%d')}.log")
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(log_entry + "\n")
        except Exception as e:
            print(f"Error writing to log file: {e}")

    def run_final_verdict_analysis(self, symbol, current_price, ml_decisions, ml_confidences,
                                 llm_decision, llm_confidence, tech_decision, mtf_decision, current_model):
        """Run final comprehensive trading verdict with all processed data"""
        try:
            if not hasattr(self, 'lmstudio_runner') or not self.lmstudio_runner:
                self.log_message("⚠️ Final Verdict: LMStudio not available - skipping comprehensive analysis")
                return

            # Get account and market data
            current_bid = getattr(self, 'current_bid', current_price)
            current_ask = getattr(self, 'current_ask', current_price)
            spread = abs(current_ask - current_bid) if current_ask and current_bid else 0.0

            # Get leverage metrics
            leverage_metrics = self.calculate_real_leverage_metrics()

            # Get market metrics
            trend_strength, volatility = self.calculate_market_metrics()

            # Mock account data (in production, get from exchange API)
            account_balance = 500.0  # USD
            free_balance = 450.0    # Available for trading
            equity = 500.0          # Total equity
            margin_used = 50.0      # Currently used margin

            # Calculate position sizing
            max_risk_per_trade = 2.0  # 2% of account
            max_risk_amount = account_balance * (max_risk_per_trade / 100)

            # Get current leverage setting from spinbox (not combo)
            current_leverage = float(self.leverage_spinbox.value())

            # Calculate optimal position size
            stop_loss_pct = 1.0  # 1% stop loss
            position_size = (max_risk_amount / (stop_loss_pct / 100)) / current_leverage
            position_size = min(position_size, free_balance * 0.8)  # Don't use more than 80% of free balance

            # Calculate take profit and stop loss levels
            if any(decision == "LONG" for decision in [llm_decision, tech_decision, mtf_decision]):
                # LONG position calculations
                entry_price = current_ask
                stop_loss_price = entry_price * (1 - stop_loss_pct / 100)
                take_profit_price = entry_price * (1 + (stop_loss_pct * 2) / 100)  # 2:1 risk/reward
                side = "LONG"
            elif any(decision == "SHORT" for decision in [llm_decision, tech_decision, mtf_decision]):
                # SHORT position calculations
                entry_price = current_bid
                stop_loss_price = entry_price * (1 + stop_loss_pct / 100)
                take_profit_price = entry_price * (1 - (stop_loss_pct * 2) / 100)  # 2:1 risk/reward
                side = "SHORT"
            else:
                # WAIT - no trade
                entry_price = current_price
                stop_loss_price = 0
                take_profit_price = 0
                side = "WAIT"

            # Get comprehensive account state for LLM awareness
            account_state = self.get_comprehensive_account_state()

            # Create comprehensive final analysis prompt
            final_prompt = f"""🎯 EPINNOX FINAL TRADING VERDICT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 COMPREHENSIVE MARKET INTELLIGENCE SUMMARY
═══════════════════════════════════════════════════════════════════════════════

🏷️ SYMBOL: {symbol}
💰 CURRENT PRICE: ${current_price:.6f}
📈 BID: ${current_bid:.6f} | ASK: ${current_ask:.6f} | SPREAD: ${spread:.6f}
⏰ ANALYSIS TIME: {self.get_current_timestamp()}

═══════════════════════════════════════════════════════════════════════════════
🤖 ML MODEL PREDICTIONS (8 MODELS)
═══════════════════════════════════════════════════════════════════════════════
{self.format_ml_predictions(ml_decisions, ml_confidences)}

═══════════════════════════════════════════════════════════════════════════════
🧠 LLM ANALYSIS RESULTS
═══════════════════════════════════════════════════════════════════════════════
🎯 LLM Decision: {llm_decision}
📊 LLM Confidence: {llm_confidence:.1f}%
🤖 Model Used: {current_model}

═══════════════════════════════════════════════════════════════════════════════
📈 TECHNICAL & MULTI-TIMEFRAME ANALYSIS
═══════════════════════════════════════════════════════════════════════════════
🔧 Technical Signal: {tech_decision}
⏱️ Multi-Timeframe: {mtf_decision}
📊 Trend Strength: {trend_strength:.3f}
📉 Volatility: {volatility:.2f}%

═══════════════════════════════════════════════════════════════════════════════
💼 ACCOUNT & RISK MANAGEMENT
═══════════════════════════════════════════════════════════════════════════════
💰 Account Balance: ${account_balance:.2f}
💵 Free Balance: ${free_balance:.2f}
📊 Equity: ${equity:.2f}
⚖️ Margin Used: ${margin_used:.2f}
🎯 Max Risk per Trade: {max_risk_per_trade}% (${max_risk_amount:.2f})

═══════════════════════════════════════════════════════════════════════════════
⚙️ LEVERAGE & POSITION SIZING
═══════════════════════════════════════════════════════════════════════════════
🔢 Current Leverage: {current_leverage}x
📏 Calculated Position Size: ${position_size:.2f}
🛡️ Stop Loss %: {stop_loss_pct}%
🎯 Suggested Entry: ${entry_price:.6f}
🛑 Stop Loss: ${stop_loss_price:.6f}
🎯 Take Profit: ${take_profit_price:.6f}
📊 Risk/Reward Ratio: 1:2

═══════════════════════════════════════════════════════════════════════════════
🤖 AUTONOMOUS TRADING SYSTEM STATUS
═══════════════════════════════════════════════════════════════════════════════
🔄 Auto Trader: {'ENABLED' if self.autonomous_trading_enabled else 'DISABLED'}
📊 Daily Trades: {account_state.get('trading_stats', {}).get('autonomous_trades_today', 0)}/{account_state.get('trading_stats', {}).get('max_daily_trades', 10)}
🚨 Emergency Stop: {'ACTIVE' if account_state.get('trading_stats', {}).get('emergency_stop_active', False) else 'INACTIVE'}

═══════════════════════════════════════════════════════════════════════════════
📊 COMPREHENSIVE ACCOUNT AWARENESS
═══════════════════════════════════════════════════════════════════════════════
{self.format_account_state_for_prompt(account_state)}

═══════════════════════════════════════════════════════════════════════════════
🎯 FINAL TRADING DIRECTIVE
═══════════════════════════════════════════════════════════════════════════════

Based on ALL the above data, provide a FINAL TRADING RECOMMENDATION in this EXACT format:

FINAL_VERDICT: [LONG/SHORT/WAIT]
CONFIDENCE: [0-100]
POSITION_SIZE: [USD amount]
ENTRY_PRICE: [exact price]
STOP_LOSS: [exact price]
TAKE_PROFIT: [exact price]
LEVERAGE: [1-200]x
RISK_LEVEL: [LOW/MEDIUM/HIGH]
REASONING: [comprehensive 2-3 sentence explanation considering ALL factors]

Consider:
- ML model consensus and confidence levels
- LLM analysis quality and reasoning
- Technical indicators alignment
- Market volatility and trend strength
- Account balance and risk management
- Current market conditions and spread
- Position sizing relative to account equity

Provide your most confident, data-driven trading recommendation."""

            # Execute final verdict analysis
            self.log_message("🎯 ═══════════════════════════════════════════════════════════════")
            self.log_message("🎯 EXECUTING FINAL TRADING VERDICT ANALYSIS")
            self.log_message("🎯 ═══════════════════════════════════════════════════════════════")

            # Log the prompt being sent to LLM
            self.log_message("🧠 LLM REQUEST: Sending comprehensive analysis prompt to LLM")
            self.log_message(f"🧠 LLM PROMPT LENGTH: {len(final_prompt)} characters")

            # Get LLM response
            final_response = self.lmstudio_runner.run_inference(final_prompt, temperature=0.3, max_tokens=400)

            if final_response:
                self.log_message(f"📝 Final Verdict Response: {final_response}")

                # Parse the structured response
                verdict_data = self.parse_final_verdict(final_response)

                if verdict_data:
                    self.log_message("🎯 ═══════════════════════════════════════════════════════════════")
                    self.log_message("🎯 FINAL TRADING RECOMMENDATION")
                    self.log_message("🎯 ═══════════════════════════════════════════════════════════════")
                    self.log_message(f"📊 VERDICT: {verdict_data['verdict']}")
                    self.log_message(f"🎯 CONFIDENCE: {verdict_data['confidence']}%")
                    self.log_message(f"💰 POSITION SIZE: ${verdict_data['position_size']:.2f}")
                    self.log_message(f"🎯 ENTRY PRICE: ${verdict_data['entry_price']:.6f}")
                    self.log_message(f"🛑 STOP LOSS: ${verdict_data['stop_loss']:.6f}")
                    self.log_message(f"🎯 TAKE PROFIT: ${verdict_data['take_profit']:.6f}")
                    self.log_message(f"⚖️ LEVERAGE: {verdict_data['leverage']}")
                    self.log_message(f"⚠️ RISK LEVEL: {verdict_data['risk_level']}")
                    self.log_message(f"💭 REASONING: {verdict_data['reasoning']}")
                    self.log_message("🎯 ═══════════════════════════════════════════════════════════════")

                    # Store final verdict for potential auto-trading
                    self.last_final_verdict = verdict_data

                    # Update GUI panel with final verdict
                    self.update_final_verdict_panel(verdict_data)

                    # Add to historical verdicts
                    self.add_to_historical_verdicts(symbol, verdict_data)

                    # Execute autonomous trade if enabled
                    if self.autonomous_trading_enabled:
                        self.log_message("🤖 AUTO TRADER: Autonomous trading enabled, executing trade decision")
                        self.execute_autonomous_trade(symbol, verdict_data)
                    else:
                        self.log_message("🤖 AUTO TRADER: Autonomous trading disabled, verdict logged only")

                else:
                    self.log_message("⚠️ Could not parse final verdict response")
            else:
                self.log_message("⚠️ No response from final verdict analysis")

        except Exception as e:
            self.log_message(f"❌ Error in final verdict analysis: {e}")

    def format_ml_predictions(self, ml_decisions, ml_confidences):
        """Format ML predictions for display"""
        model_names = ["SVM", "Random Forest", "LSTM", "RSI Model", "VWAP Model",
                      "Orderflow Model", "Volatility Model", "Sentiment Model"]

        formatted = ""
        for i, (model, decision, confidence) in enumerate(zip(model_names, ml_decisions, ml_confidences)):
            formatted += f"🤖 {model}: {decision} ({confidence:.1f}%)\n"
        return formatted.strip()

    def format_account_state_for_prompt(self, account_state):
        """Format comprehensive account state for LLM prompt"""
        try:
            if not account_state:
                return "❌ Account state unavailable"

            balance_info = account_state.get('balance_info', {})
            open_positions = account_state.get('open_positions', [])
            recent_trades = account_state.get('recent_trades', [])
            risk_metrics = account_state.get('risk_metrics', {})

            formatted = f"""💰 BALANCE INFORMATION:
   Free Balance: ${balance_info.get('free_balance', 0):.2f} USDT
   Used Balance: ${balance_info.get('used_balance', 0):.2f} USDT
   Total Balance: ${balance_info.get('total_balance', 0):.2f} USDT

📊 OPEN POSITIONS ({len(open_positions)}):"""

            if open_positions:
                for pos in open_positions:
                    formatted += f"""
   {pos['symbol']}: {pos['side'].upper()} {pos['size']:.4f} @ ${pos['entry_price']:.6f}
   Unrealized PnL: ${pos['unrealized_pnl']:.2f} | Margin: ${pos['margin_used']:.2f}"""
            else:
                formatted += "\n   No open positions"

            formatted += f"""

📈 RECENT TRADING PERFORMANCE:
   Win Rate: {risk_metrics.get('win_rate', 0):.1f}%
   Max Drawdown: {risk_metrics.get('max_drawdown', 0):.1f}%
   Average PnL: {risk_metrics.get('avg_pnl', 0):.1f}%

📋 RECENT TRADES ({len(recent_trades)}):"""

            if recent_trades:
                for trade in recent_trades[-5:]:  # Show last 5 trades
                    formatted += f"""
   {trade['time']} {trade['symbol']}: {trade['verdict']} -> {trade['result']} ({trade['pnl_percentage']:+.1f}%)"""
            else:
                formatted += "\n   No recent trades"

            return formatted

        except Exception as e:
            return f"❌ Error formatting account state: {str(e)}"

    def format_open_positions_for_prompt(self, positions):
        """Format open positions for LLM prompt"""
        if not positions:
            return "No open positions"

        formatted = ""
        for pos in positions:
            formatted += f"• {pos['symbol']}: {pos['side'].upper()} {pos['size']:.4f} @ ${pos['entry_price']:.6f} (PnL: ${pos['unrealized_pnl']:.2f})\n"
        return formatted.strip()

    def format_recent_trades_for_prompt(self, trades):
        """Format recent trades for LLM prompt"""
        if not trades:
            return "No recent trades"

        formatted = ""
        for trade in trades[-5:]:  # Last 5 trades
            formatted += f"• {trade['time']} {trade['symbol']}: {trade['verdict']} -> {trade['result']} ({trade['pnl_percentage']:+.1f}%)\n"
        return formatted.strip()

    def calculate_liquidity_score(self):
        """Calculate liquidity score based on spread and volume"""
        try:
            # Simple liquidity score based on spread
            if hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid and self.current_ask:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0

                    # Convert spread to liquidity score (lower spread = higher liquidity)
                    if spread_pct < 0.1:
                        return 1.0  # Excellent liquidity
                    elif spread_pct < 0.2:
                        return 0.8  # Good liquidity
                    elif spread_pct < 0.5:
                        return 0.6  # Fair liquidity
                    elif spread_pct < 1.0:
                        return 0.4  # Poor liquidity
                    else:
                        return 0.2  # Very poor liquidity

            return 0.5  # Default moderate liquidity

        except Exception as e:
            self.log_message(f"Error calculating liquidity score: {str(e)}")
            return 0.5

    def parse_final_verdict(self, response):
        """Parse the structured final verdict response"""
        try:
            verdict_data = {}

            # Extract each field using string parsing
            lines = response.split('\n')
            for line in lines:
                line = line.strip()

                if line.startswith('FINAL_VERDICT:'):
                    verdict_data['verdict'] = line.split(':')[1].strip()
                elif line.startswith('CONFIDENCE:'):
                    # Handle percentage sign and extract numeric value
                    confidence_str = line.split(':')[1].strip().replace('%', '')
                    # Extract just the number part (handle cases like "85% (reduced from...)")
                    import re
                    confidence_match = re.search(r'(\d+(?:\.\d+)?)', confidence_str)
                    if confidence_match:
                        verdict_data['confidence'] = float(confidence_match.group(1))
                    else:
                        verdict_data['confidence'] = 50.0  # Default fallback
                elif line.startswith('POSITION_SIZE:'):
                    # Handle position size with potential text after the number
                    position_str = line.split(':')[1].strip().replace('$', '')
                    # Extract just the number part (handle cases like "$20.00 (reduced from...)")
                    import re
                    position_match = re.search(r'(\d+(?:\.\d+)?)', position_str)
                    if position_match:
                        verdict_data['position_size'] = float(position_match.group(1))
                    else:
                        verdict_data['position_size'] = 0.0  # Default fallback
                elif line.startswith('ENTRY_PRICE:'):
                    entry_str = line.split(':')[1].strip().replace('$', '')
                    # Extract just the number part (handle cases like "$0.169612")
                    import re
                    entry_match = re.search(r'(\d+(?:\.\d+)?)', entry_str)
                    if entry_match:
                        verdict_data['entry_price'] = float(entry_match.group(1))
                    else:
                        verdict_data['entry_price'] = 0.0
                elif line.startswith('STOP_LOSS:'):
                    stop_loss_str = line.split(':')[1].strip().replace('$', '')
                    # Handle cases like "$0.000000 (1%)" or "N/A"
                    import re
                    if stop_loss_str.upper() in ['N/A', 'NONE', '--']:
                        verdict_data['stop_loss'] = 0.0
                    else:
                        stop_loss_match = re.search(r'(\d+(?:\.\d+)?)', stop_loss_str)
                        if stop_loss_match:
                            verdict_data['stop_loss'] = float(stop_loss_match.group(1))
                        else:
                            verdict_data['stop_loss'] = 0.0
                elif line.startswith('TAKE_PROFIT:'):
                    take_profit_str = line.split(':')[1].strip().replace('$', '')
                    # Handle cases like "$0.175000" or "N/A"
                    import re
                    if take_profit_str.upper() in ['N/A', 'NONE', '--']:
                        verdict_data['take_profit'] = 0.0
                    else:
                        take_profit_match = re.search(r'(\d+(?:\.\d+)?)', take_profit_str)
                        if take_profit_match:
                            verdict_data['take_profit'] = float(take_profit_match.group(1))
                        else:
                            verdict_data['take_profit'] = 0.0
                elif line.startswith('LEVERAGE:'):
                    verdict_data['leverage'] = line.split(':')[1].strip()
                elif line.startswith('RISK_LEVEL:'):
                    verdict_data['risk_level'] = line.split(':')[1].strip()
                elif line.startswith('REASONING:'):
                    verdict_data['reasoning'] = line.split(':', 1)[1].strip()

            # Validate required fields
            required_fields = ['verdict', 'confidence', 'position_size', 'entry_price']
            if all(field in verdict_data for field in required_fields):
                return verdict_data
            else:
                return None

        except Exception as e:
            self.log_message(f"Error parsing final verdict: {e}")
            return None

    def get_current_timestamp(self):
        """Get current timestamp for analysis"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def batch_gui_update(self, widget_name, update_type, value, style=None):
        """Batch GUI updates to reduce lag"""
        if widget_name not in self.pending_gui_updates:
            self.pending_gui_updates[widget_name] = {}

        self.pending_gui_updates[widget_name][update_type] = {
            'value': value,
            'style': style
        }

        # Start timer to apply updates (debounced)
        self.gui_update_timer.start(50)  # Apply updates after 50ms of inactivity

    def apply_batched_gui_updates(self):
        """Apply all pending GUI updates in a single batch"""
        try:
            for widget_name, updates in self.pending_gui_updates.items():
                widget = getattr(self, widget_name, None)
                if widget:
                    for update_type, data in updates.items():
                        if update_type == 'text' and hasattr(widget, 'setText'):
                            widget.setText(data['value'])
                        elif update_type == 'style' and hasattr(widget, 'setStyleSheet'):
                            widget.setStyleSheet(data['value'])
                        elif update_type == 'both':
                            if hasattr(widget, 'setText'):
                                widget.setText(data['value'])
                            if hasattr(widget, 'setStyleSheet') and data['style']:
                                widget.setStyleSheet(data['style'])

            # Clear pending updates
            self.pending_gui_updates.clear()

        except Exception as e:
            print(f"Error applying batched GUI updates: {e}")
            self.pending_gui_updates.clear()

    def set_performance_mode(self, enabled):
        """Enable/disable performance mode to reduce lag during analysis"""
        if enabled:
            # Pause non-critical timers during analysis
            if hasattr(self, 'bid_ask_timer'):
                self.bid_ask_timer.stop()
            if hasattr(self, 'balance_timer'):
                self.balance_timer.stop()
            if hasattr(self, 'chart_timer'):
                self.chart_timer.stop()
        else:
            # Resume timers after analysis
            if hasattr(self, 'bid_ask_timer'):
                self.bid_ask_timer.start(3000)
            if hasattr(self, 'balance_timer'):
                self.balance_timer.start(30000)
            if hasattr(self, 'chart_timer'):
                self.chart_timer.start(10000)

    def add_to_historical_verdicts(self, symbol, verdict_data):
        """Add a final verdict to historical records with tracking"""
        try:
            from datetime import datetime
            import uuid

            # Generate unique ID for tracking
            verdict_id = str(uuid.uuid4())[:8]

            # Create historical record with enhanced tracking data
            historical_record = {
                'id': verdict_id,
                'timestamp': datetime.now(),
                'symbol': symbol,
                'verdict': verdict_data.get('verdict', 'WAIT'),
                'confidence': verdict_data.get('confidence', 0),
                'entry_price': verdict_data.get('entry_price', 0),
                'stop_loss': verdict_data.get('stop_loss', 0),
                'take_profit': verdict_data.get('take_profit', 0),
                'position_size': verdict_data.get('position_size', 0),
                'leverage': verdict_data.get('leverage', '1x'),
                'risk_level': verdict_data.get('risk_level', 'LOW'),
                'reasoning': verdict_data.get('reasoning', 'No reasoning provided'),
                'result': 'PENDING',  # PENDING, WIN, LOSS, EXPIRED
                'exit_price': None,
                'exit_reason': None,  # TP_HIT, SL_HIT, MANUAL_EXIT, EXPIRED
                'pnl': 0.0,
                'pnl_percentage': 0.0,
                'max_favorable': 0.0,  # Best price reached in favor of position
                'max_adverse': 0.0,    # Worst price reached against position
                'duration_minutes': 0,
                'is_active': True if verdict_data.get('verdict', 'WAIT') != 'WAIT' else False
            }

            # Add to historical verdicts list
            self.historical_verdicts.append(historical_record)

            # Add to active tracking if it's a tradeable verdict
            if historical_record['is_active']:
                self.active_verdicts[verdict_id] = historical_record
                self.log_message(f"🎯 Started tracking verdict {verdict_id}: {historical_record['verdict']} at ${historical_record['entry_price']:.6f}")

            # Keep only last 50 verdicts to prevent memory issues
            if len(self.historical_verdicts) > 50:
                self.historical_verdicts = self.historical_verdicts[-50:]

            # Update the historical verdicts table
            self.update_historical_verdicts_table()

            self.log_message(f"📊 Added verdict to history: {verdict_data.get('verdict', 'WAIT')} for {symbol}")

        except Exception as e:
            self.log_message(f"Error adding to historical verdicts: {e}")

    def update_historical_verdicts_table(self):
        """Update the historical verdicts table with enhanced tracking data"""
        try:
            # Clear existing rows
            self.historical_verdicts_table.setRowCount(0)

            # Add rows for each historical verdict (most recent first)
            for i, record in enumerate(reversed(self.historical_verdicts)):
                self.historical_verdicts_table.insertRow(i)

                # Time
                time_str = record['timestamp'].strftime("%H:%M:%S")
                time_item = QTableWidgetItem(time_str)
                time_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 0, time_item)

                # Symbol
                symbol_item = QTableWidgetItem(record['symbol'])
                symbol_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 1, symbol_item)

                # Verdict with color coding
                verdict_item = QTableWidgetItem(record['verdict'])
                if record['verdict'] == "LONG":
                    verdict_item.setForeground(QColor(MatrixTheme.GREEN))
                elif record['verdict'] == "SHORT":
                    verdict_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    verdict_item.setForeground(QColor(MatrixTheme.YELLOW))
                self.historical_verdicts_table.setItem(i, 2, verdict_item)

                # Entry Price
                entry_price_item = QTableWidgetItem(f"{record['entry_price']:.6f}")
                entry_price_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 3, entry_price_item)

                # Exit Price
                if record.get('exit_price'):
                    exit_price_text = f"{record['exit_price']:.6f}"
                else:
                    exit_price_text = "ACTIVE" if record.get('is_active') else "--"
                exit_price_item = QTableWidgetItem(exit_price_text)
                if record.get('is_active') and not record.get('exit_price'):
                    exit_price_item.setForeground(QColor(MatrixTheme.YELLOW))
                else:
                    exit_price_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 4, exit_price_item)

                # PnL Percentage
                pnl_pct = record.get('pnl_percentage', 0)
                if pnl_pct != 0:
                    pnl_text = f"{pnl_pct:+.1f}%"
                    pnl_color = MatrixTheme.GREEN if pnl_pct > 0 else MatrixTheme.RED
                else:
                    pnl_text = "--"
                    pnl_color = MatrixTheme.GRAY
                pnl_item = QTableWidgetItem(pnl_text)
                pnl_item.setForeground(QColor(pnl_color))
                self.historical_verdicts_table.setItem(i, 5, pnl_item)

                # Result with enhanced display
                result = record['result']
                if result == 'PENDING' and record.get('is_active'):
                    result_text = "TRACKING"
                    result_color = MatrixTheme.YELLOW
                elif result == "WIN":
                    result_text = f"WIN ({record.get('exit_reason', 'UNKNOWN')})"
                    result_color = MatrixTheme.GREEN
                elif result == "LOSS":
                    result_text = f"LOSS ({record.get('exit_reason', 'UNKNOWN')})"
                    result_color = MatrixTheme.RED
                elif result == "EXPIRED":
                    result_text = "EXPIRED"
                    result_color = MatrixTheme.GRAY
                else:
                    result_text = result
                    result_color = MatrixTheme.GRAY

                result_item = QTableWidgetItem(result_text)
                result_item.setForeground(QColor(result_color))
                self.historical_verdicts_table.setItem(i, 6, result_item)

                # Duration
                duration_minutes = record.get('duration_minutes', 0)
                if duration_minutes > 0:
                    if duration_minutes < 60:
                        duration_text = f"{duration_minutes:.0f}m"
                    else:
                        hours = duration_minutes / 60
                        duration_text = f"{hours:.1f}h"
                else:
                    duration_text = "--"
                duration_item = QTableWidgetItem(duration_text)
                duration_item.setForeground(QColor(MatrixTheme.TEXT))
                self.historical_verdicts_table.setItem(i, 7, duration_item)

            # Update summary statistics
            self.update_historical_stats()

        except Exception as e:
            self.log_message(f"Error updating historical verdicts table: {e}")

    def update_historical_stats(self):
        """Update historical verdicts summary statistics with enhanced metrics"""
        try:
            total_verdicts = len(self.historical_verdicts)

            if total_verdicts > 0:
                # Calculate success rate (only for completed verdicts)
                completed_verdicts = [v for v in self.historical_verdicts if v['result'] in ['WIN', 'LOSS']]
                active_verdicts = [v for v in self.historical_verdicts if v.get('is_active', False)]

                if completed_verdicts:
                    wins = len([v for v in completed_verdicts if v['result'] == 'WIN'])
                    success_rate = (wins / len(completed_verdicts)) * 100

                    # Calculate average PnL for completed trades
                    total_pnl = sum(v.get('pnl_percentage', 0) for v in completed_verdicts)
                    avg_pnl = total_pnl / len(completed_verdicts)
                else:
                    success_rate = 0
                    avg_pnl = 0

                # Calculate average confidence
                avg_confidence = sum(v['confidence'] for v in self.historical_verdicts) / total_verdicts

                # Update labels with enhanced information
                self.total_verdicts_label.setText(f"Total: {total_verdicts} ({len(active_verdicts)} active)")

                # Color-code success rate
                if success_rate >= 60:
                    success_color = MatrixTheme.GREEN
                elif success_rate >= 40:
                    success_color = MatrixTheme.YELLOW
                else:
                    success_color = MatrixTheme.RED

                self.success_rate_label.setText(f"Win Rate: {success_rate:.1f}%")
                self.success_rate_label.setStyleSheet(f"color: {success_color}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

                # Show average PnL instead of confidence
                if avg_pnl != 0:
                    pnl_color = MatrixTheme.GREEN if avg_pnl > 0 else MatrixTheme.RED
                    self.avg_confidence_label.setText(f"Avg PnL: {avg_pnl:+.1f}%")
                    self.avg_confidence_label.setStyleSheet(f"color: {pnl_color}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")
                else:
                    self.avg_confidence_label.setText(f"Avg Conf: {avg_confidence:.1f}%")
                    self.avg_confidence_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: {MatrixTheme.FONT_SIZE_SMALL}px;")

            else:
                self.total_verdicts_label.setText("Total: 0")
                self.success_rate_label.setText("Win Rate: 0%")
                self.avg_confidence_label.setText("Avg PnL: 0%")

        except Exception as e:
            self.log_message(f"Error updating historical stats: {e}")

    def manually_close_verdict(self, verdict_id, exit_reason="MANUAL_EXIT"):
        """Manually close an active verdict for testing purposes"""
        try:
            if verdict_id in self.active_verdicts:
                verdict = self.active_verdicts[verdict_id]

                # Get current price
                current_symbol = self.symbol_combo.currentText()
                current_price = None

                if hasattr(self, 'live_data_manager') and self.live_data_manager:
                    current_price = self.live_data_manager.get_latest_price(current_symbol)

                if current_price is None and hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                    if self.current_bid and self.current_ask:
                        current_price = (self.current_bid + self.current_ask) / 2

                if current_price:
                    verdict['exit_price'] = current_price
                    verdict['exit_reason'] = exit_reason

                    # Calculate final PnL
                    entry_price = verdict['entry_price']
                    if verdict['verdict'] == 'LONG':
                        verdict['pnl'] = (current_price - entry_price) * verdict['position_size']
                        verdict['pnl_percentage'] = ((current_price - entry_price) / entry_price) * 100
                    elif verdict['verdict'] == 'SHORT':
                        verdict['pnl'] = (entry_price - current_price) * verdict['position_size']
                        verdict['pnl_percentage'] = ((entry_price - current_price) / entry_price) * 100

                    # Determine result
                    if verdict['pnl_percentage'] > 0:
                        verdict['result'] = 'WIN'
                    else:
                        verdict['result'] = 'LOSS'

                    verdict['is_active'] = False

                    # Remove from active tracking
                    del self.active_verdicts[verdict_id]

                    self.log_message(f"🔒 Manually closed verdict {verdict_id}: {verdict['result']} - PnL: {verdict['pnl_percentage']:+.1f}%")

                    # Update table
                    self.update_historical_verdicts_table()

                    return True

        except Exception as e:
            self.log_message(f"Error manually closing verdict: {e}")

        return False

    def update_verdict_tracking(self):
        """Update tracking for active verdicts based on current market prices"""
        try:
            if not self.active_verdicts:
                return

            # Get current symbol and price
            current_symbol = self.symbol_combo.currentText()
            current_price = None

            # Try to get current price from live data manager
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                current_price = self.live_data_manager.get_latest_price(current_symbol)

            # Fallback to bid/ask average if available
            if current_price is None and hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid and self.current_ask:
                    current_price = (self.current_bid + self.current_ask) / 2

            if current_price is None:
                return

            # Track each active verdict
            completed_verdicts = []

            for verdict_id, verdict in self.active_verdicts.items():
                if verdict['symbol'] != current_symbol:
                    continue

                # Calculate duration
                from datetime import datetime
                duration = (datetime.now() - verdict['timestamp']).total_seconds() / 60
                verdict['duration_minutes'] = duration

                # Update max favorable/adverse prices
                entry_price = verdict['entry_price']

                if verdict['verdict'] == 'LONG':
                    # For LONG positions
                    if current_price > entry_price:
                        # Favorable move
                        verdict['max_favorable'] = max(verdict['max_favorable'], current_price - entry_price)
                    else:
                        # Adverse move
                        verdict['max_adverse'] = max(verdict['max_adverse'], entry_price - current_price)

                    # Check stop loss hit
                    if verdict['stop_loss'] > 0 and current_price <= verdict['stop_loss']:
                        verdict['result'] = 'LOSS'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'SL_HIT'
                        completed_verdicts.append(verdict_id)

                    # Check take profit hit
                    elif verdict['take_profit'] > 0 and current_price >= verdict['take_profit']:
                        verdict['result'] = 'WIN'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'TP_HIT'
                        completed_verdicts.append(verdict_id)

                elif verdict['verdict'] == 'SHORT':
                    # For SHORT positions
                    if current_price < entry_price:
                        # Favorable move
                        verdict['max_favorable'] = max(verdict['max_favorable'], entry_price - current_price)
                    else:
                        # Adverse move
                        verdict['max_adverse'] = max(verdict['max_adverse'], current_price - entry_price)

                    # Check stop loss hit
                    if verdict['stop_loss'] > 0 and current_price >= verdict['stop_loss']:
                        verdict['result'] = 'LOSS'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'SL_HIT'
                        completed_verdicts.append(verdict_id)

                    # Check take profit hit
                    elif verdict['take_profit'] > 0 and current_price <= verdict['take_profit']:
                        verdict['result'] = 'WIN'
                        verdict['exit_price'] = current_price
                        verdict['exit_reason'] = 'TP_HIT'
                        completed_verdicts.append(verdict_id)

                # Check for expiration (24 hours)
                if duration > 1440:  # 24 hours in minutes
                    verdict['result'] = 'EXPIRED'
                    verdict['exit_price'] = current_price
                    verdict['exit_reason'] = 'EXPIRED'
                    completed_verdicts.append(verdict_id)

                # Calculate current PnL
                if verdict['exit_price']:
                    exit_price = verdict['exit_price']
                else:
                    exit_price = current_price

                if verdict['verdict'] == 'LONG':
                    verdict['pnl'] = (exit_price - entry_price) * verdict['position_size']
                    verdict['pnl_percentage'] = ((exit_price - entry_price) / entry_price) * 100
                elif verdict['verdict'] == 'SHORT':
                    verdict['pnl'] = (entry_price - exit_price) * verdict['position_size']
                    verdict['pnl_percentage'] = ((entry_price - exit_price) / entry_price) * 100

            # Remove completed verdicts from active tracking
            for verdict_id in completed_verdicts:
                verdict = self.active_verdicts[verdict_id]
                self.log_message(f"🏁 Verdict {verdict_id} completed: {verdict['result']} - {verdict['exit_reason']} - PnL: ${verdict['pnl']:.2f} ({verdict['pnl_percentage']:.1f}%)")
                del self.active_verdicts[verdict_id]

            # Update the historical verdicts table if any changes occurred
            if completed_verdicts or self.active_verdicts:
                self.update_historical_verdicts_table()

        except Exception as e:
            self.log_message(f"Error updating verdict tracking: {e}")

    def update_final_verdict_panel(self, verdict_data):
        """Update the final verdict GUI panel with analysis results"""
        try:
            # Update main verdict label
            verdict = verdict_data.get('verdict', 'WAIT')
            confidence = verdict_data.get('confidence', 0)

            self.final_verdict_label.setText(f"Final Verdict: {verdict} ({confidence:.1f}%)")

            # Color code based on verdict
            if verdict == "LONG":
                color = MatrixTheme.GREEN
            elif verdict == "SHORT":
                color = MatrixTheme.RED
            else:
                color = MatrixTheme.YELLOW

            self.final_verdict_label.setStyleSheet(f"""
                font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
                font-weight: bold;
                color: {color};
                padding: 8px;
                border: 2px solid {color};
                border-radius: 5px;
                background-color: {MatrixTheme.BLACK};
                text-align: center;
            """)

            # Update trading parameters
            self.final_position_size_label.setText(f"${verdict_data.get('position_size', 0):.2f}")
            self.final_entry_price_label.setText(f"${verdict_data.get('entry_price', 0):.6f}")
            self.final_stop_loss_label.setText(f"${verdict_data.get('stop_loss', 0):.6f}")
            self.final_take_profit_label.setText(f"${verdict_data.get('take_profit', 0):.6f}")
            self.final_leverage_label.setText(verdict_data.get('leverage', '1x'))
            self.final_risk_level_label.setText(verdict_data.get('risk_level', 'LOW'))
            self.final_confidence_label.setText(f"{confidence:.1f}%")

            # Color code risk level
            risk_level = verdict_data.get('risk_level', 'LOW')
            if risk_level == "HIGH":
                risk_color = MatrixTheme.RED
            elif risk_level == "MEDIUM":
                risk_color = MatrixTheme.YELLOW
            else:
                risk_color = MatrixTheme.GREEN

            self.final_risk_level_label.setStyleSheet(f"color: {risk_color}; font-weight: bold;")

            # Update reasoning
            reasoning = verdict_data.get('reasoning', 'No reasoning provided')
            self.final_reasoning_text.setText(reasoning)

        except Exception as e:
            self.log_message(f"Error updating final verdict panel: {e}")

    # Comprehensive Analysis Methods
    def run_comprehensive_analysis(self, symbol, current_price, ml_decisions, ml_confidences,
                                  llm_decision, llm_confidence, llm_reasoning, current_model):
        """Run comprehensive second-stage analysis with all available data"""
        try:
            if not hasattr(self, 'lmstudio_runner') or not self.lmstudio_runner:
                return

            # Get additional market data
            current_bid = getattr(self, 'current_bid', current_price)
            current_ask = getattr(self, 'current_ask', current_price)
            spread = abs(current_ask - current_bid) if current_ask and current_bid else 0.0

            # Get technical analysis data (mock for now)
            tech_decision = "WAIT"  # This would come from technical analysis
            tech_confidence = 75.0
            mtf_decision = "LONG"   # This would come from multi-timeframe analysis
            mtf_confidence = 68.0

            # Create comprehensive analysis prompt
            comprehensive_prompt = f"""🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: {symbol}
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: ${current_price:.6f}
📈 Bid: ${current_bid:.6f} | Ask: ${current_ask:.6f} | Spread: ${spread:.6f}
⏰ Analysis Time: {self.get_current_timestamp()}

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANALYSIS
═══════════════════════════════════════════════════════════════════════════════
🔹 SVM Model: {ml_decisions[0]} ({ml_confidences[0]:.1f}% confidence)
🔹 Random Forest: {ml_decisions[1]} ({ml_confidences[1]:.1f}% confidence)
🔹 LSTM Neural Network: {ml_decisions[2]} ({ml_confidences[2]:.1f}% confidence)
📊 ML Ensemble Average: {sum(ml_confidences)/3:.1f}% confidence

═══════════════════════════════════════════════════════════════════════════════
🧠 INITIAL LLM ANALYSIS ({current_model})
═══════════════════════════════════════════════════════════════════════════════
🎯 Decision: {llm_decision} ({llm_confidence:.1f}% confidence)
💭 Reasoning: {llm_reasoning[:200]}...

═══════════════════════════════════════════════════════════════════════════════
📈 TECHNICAL & MULTI-TIMEFRAME SIGNALS
═══════════════════════════════════════════════════════════════════════════════
🔧 Technical Analysis: {tech_decision} ({tech_confidence:.1f}% confidence)
⏱️ Multi-Timeframe: {mtf_decision} ({mtf_confidence:.1f}% confidence)

═══════════════════════════════════════════════════════════════════════════════
🎭 CREATIVE SYNTHESIS CHALLENGE
═══════════════════════════════════════════════════════════════════════════════

You are the MASTER TRADING STRATEGIST for the Epinnox AI Trading System.

Your mission: Synthesize ALL the above intelligence into a CREATIVE, COMPREHENSIVE trading strategy that considers:

1. 🎪 CONSENSUS ANALYSIS: How do all signals align or conflict?
2. 🎨 CREATIVE RISK ASSESSMENT: What unique risks/opportunities do you see?
3. 🎯 STRATEGIC POSITIONING: What's the optimal position size and timing?
4. 🎲 SCENARIO PLANNING: Best/worst case scenarios and contingencies
5. 🎪 MARKET PSYCHOLOGY: What emotions are driving this market?

Respond in this EXACT format:
FINAL_DECISION: [LONG/SHORT/WAIT]
CONFIDENCE: [0-100]
POSITION_SIZE: [SMALL/MEDIUM/LARGE/NONE]
ENTRY_STRATEGY: [IMMEDIATE/GRADUAL/WAIT_FOR_DIP/WAIT_FOR_BREAKOUT]
RISK_LEVEL: [LOW/MEDIUM/HIGH/EXTREME]
CREATIVE_INSIGHT: [Your most creative market insight in 1-2 sentences]
SYNTHESIS: [Comprehensive reasoning combining all signals and your creative analysis]"""

            self.log_message("🎭 Starting Comprehensive Creative Analysis...")
            self.log_message("═" * 80)

            # Get comprehensive response
            comprehensive_response = self.lmstudio_runner.run_inference(
                prompt=comprehensive_prompt,
                temperature=0.8,  # Higher creativity
                max_tokens=500    # More detailed response
            )

            # Parse and display comprehensive analysis
            self.display_comprehensive_analysis(comprehensive_response, symbol, current_model)

        except Exception as e:
            self.log_message(f"❌ Error in comprehensive analysis: {e}")

    def get_current_timestamp(self):
        """Get formatted current timestamp"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

    def display_comprehensive_analysis(self, response, symbol, model):
        """Display comprehensive analysis in interface and terminal"""
        try:
            self.log_message("🎭 COMPREHENSIVE ANALYSIS COMPLETE")
            self.log_message("═" * 80)
            self.log_message(f"📝 Full Creative Response:")
            self.log_message(response)
            self.log_message("═" * 80)

            # Parse structured response
            parsed_data = self.parse_comprehensive_response(response)

            # Display in terminal with creative formatting
            self.display_creative_terminal_output(parsed_data, symbol, model)

            # Update GUI with comprehensive data
            self.update_comprehensive_gui(parsed_data)

        except Exception as e:
            self.log_message(f"❌ Error displaying comprehensive analysis: {e}")

    def parse_comprehensive_response(self, response):
        """Parse the structured comprehensive response"""
        parsed = {
            'final_decision': 'WAIT',
            'confidence': 50.0,
            'position_size': 'NONE',
            'entry_strategy': 'WAIT_FOR_SIGNAL',
            'risk_level': 'MEDIUM',
            'creative_insight': 'Analysis in progress...',
            'synthesis': 'Comprehensive analysis completed.'
        }

        if not response:
            return parsed

        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('FINAL_DECISION:'):
                parsed['final_decision'] = line.split(':', 1)[1].strip()
            elif line.startswith('CONFIDENCE:'):
                try:
                    parsed['confidence'] = float(line.split(':', 1)[1].strip())
                except:
                    pass
            elif line.startswith('POSITION_SIZE:'):
                parsed['position_size'] = line.split(':', 1)[1].strip()
            elif line.startswith('ENTRY_STRATEGY:'):
                parsed['entry_strategy'] = line.split(':', 1)[1].strip()
            elif line.startswith('RISK_LEVEL:'):
                parsed['risk_level'] = line.split(':', 1)[1].strip()
            elif line.startswith('CREATIVE_INSIGHT:'):
                parsed['creative_insight'] = line.split(':', 1)[1].strip()
            elif line.startswith('SYNTHESIS:'):
                parsed['synthesis'] = line.split(':', 1)[1].strip()

        return parsed

    def display_creative_terminal_output(self, parsed_data, symbol, model):
        """Display creative formatted output in terminal"""
        try:
            # Creative ASCII art header
            self.log_message("🎭" + "═" * 78 + "🎭")
            self.log_message("🎪           EPINNOX CREATIVE TRADING INTELLIGENCE REPORT           🎪")
            self.log_message("🎭" + "═" * 78 + "🎭")
            self.log_message(f"🎯 Symbol: {symbol} | Model: {model} | Time: {self.get_current_timestamp()}")
            self.log_message("🎭" + "─" * 78 + "🎭")

            # Decision with creative formatting
            decision = parsed_data['final_decision']
            confidence = parsed_data['confidence']

            if decision == "LONG":
                decision_emoji = "🚀📈"
            elif decision == "SHORT":
                decision_emoji = "🔻📉"
            else:
                decision_emoji = "⏸️⚖️"

            self.log_message(f"🎯 FINAL DECISION: {decision_emoji} {decision} ({confidence:.1f}% confidence)")
            self.log_message(f"📊 POSITION SIZE: {parsed_data['position_size']}")
            self.log_message(f"⚡ ENTRY STRATEGY: {parsed_data['entry_strategy']}")
            self.log_message(f"⚠️ RISK LEVEL: {parsed_data['risk_level']}")
            self.log_message("🎭" + "─" * 78 + "🎭")
            self.log_message(f"💡 CREATIVE INSIGHT: {parsed_data['creative_insight']}")
            self.log_message("🎭" + "─" * 78 + "🎭")
            self.log_message(f"🧠 SYNTHESIS: {parsed_data['synthesis']}")
            self.log_message("🎭" + "═" * 78 + "🎭")

        except Exception as e:
            self.log_message(f"❌ Error in creative terminal display: {e}")

    def update_comprehensive_gui(self, parsed_data):
        """Update GUI with comprehensive analysis data"""
        try:
            # Update status bar with comprehensive info
            decision = parsed_data['final_decision']
            confidence = parsed_data['confidence']
            risk = parsed_data['risk_level']

            status_msg = f"🎭 COMPREHENSIVE: {decision} ({confidence:.1f}%) | Risk: {risk} | Strategy: {parsed_data['entry_strategy']}"
            self.statusBar().showMessage(status_msg)

            # Add comprehensive analysis to the analysis log
            comprehensive_summary = f"""
🎭 COMPREHENSIVE ANALYSIS COMPLETE:
• Final Decision: {decision} ({confidence:.1f}% confidence)
• Position Size: {parsed_data['position_size']}
• Entry Strategy: {parsed_data['entry_strategy']}
• Risk Level: {parsed_data['risk_level']}
• Creative Insight: {parsed_data['creative_insight']}
"""
            print(comprehensive_summary)  # Log to terminal instead of removed analysis_log panel

            # Update comprehensive analysis panel
            if hasattr(self, 'comprehensive_decision_label'):
                decision_text = f"🎭 {decision} ({confidence:.1f}%) | {parsed_data['position_size']} | {parsed_data['risk_level']} Risk"
                self.comprehensive_decision_label.setText(decision_text)

            if hasattr(self, 'creative_insight_text'):
                insight_text = f"💡 CREATIVE INSIGHT:\n{parsed_data['creative_insight']}\n\n🧠 SYNTHESIS:\n{parsed_data['synthesis']}"
                self.creative_insight_text.setText(insight_text)

        except Exception as e:
            self.log_message(f"❌ Error updating comprehensive GUI: {e}")

    # LMStudio Model Management Methods
    def on_model_switch_requested(self, model_name: str):
        """Handle model switch request from model selector"""
        try:
            # Initialize LMStudio runner if not already done
            if not hasattr(self, 'lmstudio_runner'):
                self.setup_lmstudio_runner()

            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                success = self.lmstudio_runner.switch_model(model_name)
                if success:
                    self.log_message(f"🔄 Switched to model: {model_name}")
                    self.statusBar().showMessage(f"Model switched to: {model_name}")
                else:
                    self.log_message(f"❌ Failed to switch to model: {model_name}")
            else:
                self.log_message("❌ LMStudio runner not available")

        except Exception as e:
            self.log_message(f"Error switching model: {e}")

    def on_model_refresh_requested(self):
        """Handle model refresh request from model selector"""
        try:
            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                success = self.lmstudio_runner.refresh_models()
                if success:
                    models = self.lmstudio_runner.get_available_models()
                    if hasattr(self, 'model_selector'):
                        self.model_selector.update_models(models)
                        self.model_selector.set_connection_status(True)
                    self.log_message(f"🔄 Refreshed models: {len(models)} found")
                else:
                    if hasattr(self, 'model_selector'):
                        self.model_selector.set_connection_status(False)
                    self.log_message("❌ Failed to refresh models")
            else:
                self.setup_lmstudio_runner()

        except Exception as e:
            self.log_message(f"Error refreshing models: {e}")

    def setup_lmstudio_runner(self):
        """Setup LMStudio runner with model discovery"""
        try:
            from llama.lmstudio_runner import LMStudioRunner

            self.lmstudio_runner = LMStudioRunner()

            # Connect signals
            self.lmstudio_runner.model_changed.connect(self.on_lmstudio_model_changed)
            self.lmstudio_runner.models_discovered.connect(self.on_lmstudio_models_discovered)

            # Initial model discovery
            models = self.lmstudio_runner.get_available_models()
            current_model = self.lmstudio_runner.get_current_model()

            if hasattr(self, 'model_selector'):
                self.model_selector.update_models(models)
                if current_model:
                    self.model_selector.set_current_model(current_model)
                self.model_selector.set_connection_status(len(models) > 0)

            self.log_message(f"✓ LMStudio runner initialized with {len(models)} models")

        except Exception as e:
            self.log_message(f"Error setting up LMStudio runner: {e}")

    def on_lmstudio_model_changed(self, model_name: str):
        """Handle LMStudio model change signal"""
        try:
            if hasattr(self, 'model_selector'):
                self.model_selector.set_current_model(model_name)
            self.log_message(f"✓ LMStudio model changed to: {model_name}")

        except Exception as e:
            self.log_message(f"Error handling model change: {e}")

    def on_lmstudio_models_discovered(self, models: list):
        """Handle LMStudio models discovery signal"""
        try:
            if hasattr(self, 'model_selector'):
                self.model_selector.update_models(models)
                self.model_selector.set_connection_status(len(models) > 0)
            self.log_message(f"✓ Discovered {len(models)} LMStudio models")

        except Exception as e:
            self.log_message(f"Error handling models discovery: {e}")

    # Manual Trading Methods
    def place_limit_long(self):
        """Place a limit long order using best bid price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ Limit LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit LONG order placed", 3000)
                else:
                    self.log_message(f"❌ Failed to place limit LONG order")
                    self.statusBar().showMessage("Failed to place limit LONG order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'bids' not in ob or not ob['bids']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['bids'][0][0]  # Use best bid price

            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'buy', quantity, price, params)

            if result:
                self.log_message(f"Limit LONG: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit LONG placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit LONG order")
                self.statusBar().showMessage("Failed to place limit LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_limit_short(self):
        """Place a limit short order using best ask price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ Limit SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit SHORT order placed", 3000)
                else:
                    self.log_message(f"❌ Failed to place limit SHORT order")
                    self.statusBar().showMessage("Failed to place limit SHORT order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'asks' not in ob or not ob['asks']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['asks'][0][0]  # Use best ask price

            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'sell', quantity, price, params)

            if result:
                self.log_message(f"Limit SHORT: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit SHORT placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit SHORT order")
                self.statusBar().showMessage("Failed to place limit SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_long(self):
        """Place a market long order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market LONG executed", 3000)
                else:
                    self.log_message(f"Failed to place market LONG order")
                    self.statusBar().showMessage("Failed to place market LONG order", 3000)
                return

            # Fallback to original implementation
            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'buy', quantity, params)

            if result:
                self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market LONG executed", 3000)
            else:
                self.log_message(f"Failed to place market LONG order")
                self.statusBar().showMessage("Failed to place market LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_short(self):
        """Place a market short order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market SHORT executed", 3000)
                else:
                    self.log_message(f"Failed to place market SHORT order")
                    self.statusBar().showMessage("Failed to place market SHORT order", 3000)
                return

            # Fallback to original implementation
            # Set leverage using real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_leverage(symbol, leverage)
            else:
                set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'sell', quantity, params)

            if result:
                self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market SHORT executed", 3000)
            else:
                self.log_message(f"Failed to place market SHORT order")
                self.statusBar().showMessage("Failed to place market SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def close_all_positions(self):
        """Close all open positions"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.close_all_positions()
                if count > 0:
                    self.log_message(f"Closed {count} positions")
                    self.statusBar().showMessage(f"Closed {count} positions", 3000)
                else:
                    self.log_message("No positions to close")
                    self.statusBar().showMessage("No positions to close", 3000)
                return

            # Fallback to original implementation
            result = close_all_positions()
            if result:
                self.log_message("All positions closed")
                self.statusBar().showMessage("All positions closed", 3000)
            else:
                self.log_message("Failed to close positions")
                self.statusBar().showMessage("Failed to close positions", 3000)
        except Exception as e:
            self.log_message(f"Error closing positions: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.cancel_all_orders()
                if count > 0:
                    self.log_message(f"Cancelled {count} orders")
                    self.statusBar().showMessage(f"Cancelled {count} orders", 3000)
                else:
                    self.log_message("No orders to cancel")
                    self.statusBar().showMessage("No orders to cancel", 3000)
                return

            # Fallback to original implementation
            result = cancel_all_orders()
            if result:
                self.log_message("All orders cancelled")
                self.statusBar().showMessage("All orders cancelled", 3000)
            else:
                self.log_message("Failed to cancel orders")
                self.statusBar().showMessage("Failed to cancel orders", 3000)
        except Exception as e:
            self.log_message(f"Error cancelling orders: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def on_leverage_changed(self, leverage: int):
        """Handle leverage spinbox value changes"""
        try:
            symbol = self.symbol_combo.currentText()

            # Update real trading interface leverage
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.set_leverage(symbol, leverage)
                if success:
                    self.log_message(f"🔧 Leverage updated to {leverage}x for {symbol}")
                else:
                    self.log_message(f"⚠️ Failed to update leverage to {leverage}x for {symbol}")

            # Update current leverage in real trading interface
            if hasattr(self, 'real_trading') and self.real_trading:
                self.real_trading.set_current_leverage(leverage)

        except Exception as e:
            self.log_message(f"Error updating leverage: {str(e)}")

    def on_auto_trader_toggled(self, state):
        """Handle autonomous trading toggle"""
        try:
            self.autonomous_trading_enabled = bool(state)

            if self.autonomous_trading_enabled:
                # Enable autonomous trading
                self.log_message("🤖 AUTONOMOUS TRADING ENABLED - AI will execute trades automatically")
                self.log_message("⚠️ WARNING: AI will place real trades based on analysis. Monitor carefully!")

                # Debug: Check real trading interface status
                if hasattr(self, 'real_trading') and self.real_trading:
                    self.log_message(f"🔍 Real trading interface available: {type(self.real_trading).__name__}")
                    self.log_message(f"🔍 Demo mode: {self.real_trading.is_demo_mode()}")
                    self.log_message(f"🔍 Connected: {self.real_trading.is_connected()}")
                else:
                    self.log_message("❌ Real trading interface not available")

                # Reset daily stats if it's a new day
                self.reset_daily_trading_stats_if_needed()

                # Check safety conditions
                if not self.check_autonomous_trading_safety():
                    self.auto_trader_checkbox.setChecked(False)
                    self.autonomous_trading_enabled = False
                    return

            else:
                # Disable autonomous trading
                self.log_message("🤖 AUTONOMOUS TRADING DISABLED - Manual control restored")

        except Exception as e:
            self.log_message(f"Error toggling auto trader: {str(e)}")

    def check_autonomous_trading_safety(self):
        """Check if autonomous trading is safe to enable"""
        try:
            # Check if emergency stop is triggered
            if self.autonomous_trading_stats['emergency_stop_triggered']:
                self.log_message("❌ Emergency stop is active. Cannot enable autonomous trading.")
                return False

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"❌ Daily trade limit reached ({self.autonomous_trading_stats['max_daily_trades']}). Cannot enable autonomous trading.")
                return False

            # Check if real trading interface is available
            if not hasattr(self, 'real_trading') or not self.real_trading:
                self.log_message("❌ Real trading interface not available. Cannot enable autonomous trading.")
                return False

            # Check account balance
            account_state = self.get_comprehensive_account_state()
            if account_state:
                balance_info = account_state.get('balance_info', {})
                free_balance = balance_info.get('free_balance', 0)
                self.log_message(f"🔍 Checking balance for autonomous trading: ${free_balance:.2f} USDT")
                if free_balance < 10:  # Minimum $10 balance
                    self.log_message(f"❌ Insufficient balance for autonomous trading (${free_balance:.2f} < $10.00 required).")
                    return False
            else:
                self.log_message("❌ Could not retrieve account state for balance check.")
                return False

            self.log_message("✅ Autonomous trading safety checks passed")
            return True

        except Exception as e:
            self.log_message(f"Error checking autonomous trading safety: {str(e)}")
            return False

    def reset_daily_trading_stats_if_needed(self):
        """Reset daily trading stats if it's a new day"""
        try:
            from datetime import datetime, date

            today = date.today()
            last_trade_date = None

            if self.autonomous_trading_stats['last_trade_time']:
                last_trade_date = self.autonomous_trading_stats['last_trade_time'].date()

            if last_trade_date != today:
                self.autonomous_trading_stats['daily_trades'] = 0
                self.log_message("📅 Daily trading stats reset for new day")

        except Exception as e:
            self.log_message(f"Error resetting daily stats: {str(e)}")

    def get_comprehensive_account_state(self):
        """Get comprehensive account state for LLM decision making"""
        try:
            account_state = {
                'timestamp': datetime.now().isoformat(),
                'balance_info': {},
                'open_positions': [],
                'recent_trades': [],
                'risk_metrics': {},
                'market_conditions': {},
                'trading_stats': {},
                'leverage_settings': {}
            }

            # Get balance information from futures account
            if hasattr(self, 'real_trading') and self.real_trading:
                # Get futures account balance specifically using the correct method
                balance = self.real_trading.get_balance_info()
                # self.log_message(f"🔍 Retrieved balance from real trading interface: {balance}")

                if balance:
                    usdt_info = balance.get('USDT', {})
                    free_balance = usdt_info.get('free', 0)
                    used_balance = usdt_info.get('used', 0)
                    total_balance = usdt_info.get('total', 0)

                    account_state['balance_info'] = {
                        'free_balance': free_balance,
                        'used_balance': used_balance,
                        'total_balance': total_balance,
                        'currency': 'USDT'
                    }

                    self.log_message(f"💰 Account Balance Info: Free=${free_balance:.2f}, Used=${used_balance:.2f}, Total=${total_balance:.2f}")
                else:
                    self.log_message("⚠️ No balance data received from real trading interface")
                    account_state['balance_info'] = {
                        'free_balance': 0,
                        'used_balance': 0,
                        'total_balance': 0,
                        'currency': 'USDT'
                    }

                # Get open positions using the correct method
                positions = self.real_trading.get_all_positions()
                # self.log_message(f"🔍 Retrieved positions data: {positions}")

                if positions:
                    for symbol, position_data in positions.items():
                        # self.log_message(f"🔍 Processing position for {symbol}: {position_data}")

                        # Check multiple possible size fields
                        size = 0
                        if 'contracts' in position_data:
                            size = position_data.get('contracts', 0)
                        elif 'size' in position_data:
                            size = position_data.get('size', 0)
                        elif 'amount' in position_data:
                            size = position_data.get('amount', 0)
                        elif 'quantity' in position_data:
                            size = position_data.get('quantity', 0)

                        if size != 0:  # Only include non-zero positions
                            account_state['open_positions'].append({
                                'symbol': symbol,
                                'side': position_data.get('side', 'unknown'),
                                'size': size,
                                'entry_price': position_data.get('entryPrice', position_data.get('contract_value', position_data.get('entry_price', position_data.get('average_price', 0)))),
                                'unrealized_pnl': position_data.get('unrealizedPnl', position_data.get('profit', position_data.get('unrealized_pnl', position_data.get('pnl', 0)))),
                                'margin_used': position_data.get('initialMargin', position_data.get('margin_balance', position_data.get('margin', position_data.get('initial_margin', 0))))
                            })

            # Get recent trading history from historical verdicts
            recent_verdicts = self.historical_verdicts[-10:] if self.historical_verdicts else []
            account_state['recent_trades'] = [
                {
                    'time': verdict.get('timestamp', '').strftime('%H:%M:%S') if verdict.get('timestamp') else 'unknown',
                    'symbol': verdict.get('symbol', 'unknown'),
                    'verdict': verdict.get('verdict', 'unknown'),
                    'result': verdict.get('result', 'PENDING'),
                    'pnl_percentage': verdict.get('pnl_percentage', 0)
                }
                for verdict in recent_verdicts
            ]

            # Calculate risk metrics
            account_state['risk_metrics'] = self.calculate_risk_metrics()

            # Get market conditions
            account_state['market_conditions'] = self.get_market_conditions()

            # Get current leverage settings
            account_state['leverage_settings'] = {
                'current_leverage': self.leverage_spinbox.value(),
                'max_leverage': 125,
                'leverage_source': 'UI_spinbox_readonly'
            }

            # Get trading statistics
            account_state['trading_stats'] = {
                'total_historical_trades': len(self.historical_verdicts),
                'autonomous_trades_today': self.autonomous_trading_stats['daily_trades'],
                'max_daily_trades': self.autonomous_trading_stats['max_daily_trades'],
                'emergency_stop_active': self.autonomous_trading_stats['emergency_stop_triggered']
            }

            return account_state

        except Exception as e:
            self.log_message(f"Error getting comprehensive account state: {str(e)}")
            return None

    def calculate_risk_metrics(self):
        """Calculate comprehensive risk metrics"""
        try:
            risk_metrics = {
                'portfolio_risk': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'avg_pnl': 0.0,
                'correlation_risk': 'LOW',
                'liquidity_risk': 'LOW',
                'exposure_ratio': 0.0
            }

            if self.historical_verdicts:
                completed_trades = [v for v in self.historical_verdicts if v.get('result') in ['WIN', 'LOSS']]

                if completed_trades:
                    # Calculate win rate
                    wins = len([v for v in completed_trades if v.get('result') == 'WIN'])
                    risk_metrics['win_rate'] = (wins / len(completed_trades)) * 100

                    # Calculate average PnL
                    total_pnl = sum(v.get('pnl_percentage', 0) for v in completed_trades)
                    risk_metrics['avg_pnl'] = total_pnl / len(completed_trades)

                    # Calculate max drawdown (simplified)
                    pnl_values = [v.get('pnl_percentage', 0) for v in completed_trades]
                    if pnl_values:
                        cumulative_pnl = []
                        running_total = 0
                        for pnl in pnl_values:
                            running_total += pnl
                            cumulative_pnl.append(running_total)

                        peak = cumulative_pnl[0]
                        max_drawdown = 0
                        for value in cumulative_pnl:
                            if value > peak:
                                peak = value
                            drawdown = peak - value
                            if drawdown > max_drawdown:
                                max_drawdown = drawdown

                        risk_metrics['max_drawdown'] = max_drawdown

            return risk_metrics

        except Exception as e:
            self.log_message(f"Error calculating risk metrics: {str(e)}")
            return {}

    def get_market_conditions(self):
        """Get current market conditions"""
        try:
            market_conditions = {
                'volatility': 0.0,
                'trend_strength': 0.0,
                'liquidity_score': 0.0,
                'spread': 0.0,
                'market_regime': 'UNKNOWN'
            }

            # Get current market metrics
            trend_strength, volatility = self.calculate_market_metrics()
            market_conditions['trend_strength'] = trend_strength
            market_conditions['volatility'] = volatility

            # Get liquidity score
            liquidity_score = self.calculate_liquidity_score()
            market_conditions['liquidity_score'] = liquidity_score

            # Calculate spread
            if hasattr(self, 'current_bid') and hasattr(self, 'current_ask'):
                if self.current_bid and self.current_ask:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                    market_conditions['spread'] = spread_pct

            return market_conditions

        except Exception as e:
            self.log_message(f"Error getting market conditions: {str(e)}")
            return {}

    def execute_autonomous_trade(self, symbol, verdict_data):
        """Execute autonomous trade based on LLM verdict with comprehensive safety checks"""
        try:
            self.log_message("🤖 AUTO TRADER: Starting autonomous trade execution")
            self.log_message(f"🤖 AUTO TRADER: Symbol: {symbol}")
            self.log_message(f"🤖 AUTO TRADER: Verdict Data: {verdict_data}")

            # Get comprehensive account state for decision validation
            account_state = self.get_comprehensive_account_state()
            if not account_state:
                self.log_message("❌ AUTO TRADER: Cannot execute autonomous trade: Unable to get account state")
                return False

            verdict = verdict_data.get('verdict', 'WAIT')
            confidence = verdict_data.get('confidence', 0)
            position_size = verdict_data.get('position_size', 0)

            self.log_message("🤖 ═══════════════════════════════════════════════════════════════")
            self.log_message("🤖 AUTONOMOUS TRADING EXECUTION INITIATED")
            self.log_message(f"🤖 VERDICT: {verdict} | CONFIDENCE: {confidence}% | SIZE: ${position_size}")
            self.log_message("🤖 ═══════════════════════════════════════════════════════════════")

            # Pre-execution safety checks
            self.log_message("🔍 AUTO TRADER: Running pre-execution safety checks")
            if not self.pre_execution_safety_checks(verdict_data, account_state):
                self.log_message("❌ AUTO TRADER: Safety checks failed, aborting trade")
                return False
            self.log_message("✅ AUTO TRADER: Safety checks passed")

            # Calculate intelligent position size
            optimal_position_size = self.calculate_intelligent_position_size(verdict_data, account_state)
            if optimal_position_size <= 0:
                self.log_message("❌ Calculated position size is zero or negative. Skipping trade.")
                return False

            # Log comprehensive decision context
            self.log_autonomous_decision_context(verdict_data, account_state, optimal_position_size)

            # Execute the trade
            self.log_message(f"🎯 AUTO TRADER: Executing {verdict} trade with ${optimal_position_size:.2f} position size")
            success = False
            if verdict == "LONG":
                self.log_message("📈 AUTO TRADER: Executing LONG position")
                success = self.execute_autonomous_long(symbol, optimal_position_size, verdict_data)
            elif verdict == "SHORT":
                self.log_message("📉 AUTO TRADER: Executing SHORT position")
                success = self.execute_autonomous_short(symbol, optimal_position_size, verdict_data)
            else:
                self.log_message(f"⏸️ AUTO TRADER: AI Decision: {verdict} - No trade executed (WAIT/HOLD)")
                return True  # WAIT is a valid decision

            # Update autonomous trading statistics
            if success:
                self.update_autonomous_trading_stats(True, verdict_data)
                self.log_message("✅ Autonomous trade executed successfully")
            else:
                self.update_autonomous_trading_stats(False, verdict_data)
                self.log_message("❌ Autonomous trade execution failed")

            return success

        except Exception as e:
            self.log_message(f"❌ Error in autonomous trade execution: {str(e)}")
            return False

    def pre_execution_safety_checks(self, verdict_data, account_state):
        """Comprehensive pre-execution safety checks"""
        try:
            # Check emergency stop
            if self.autonomous_trading_stats['emergency_stop_triggered']:
                self.log_message("❌ Emergency stop is active. Trade cancelled.")
                return False

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"❌ Daily trade limit reached ({self.autonomous_trading_stats['max_daily_trades']}). Trade cancelled.")
                self.autonomous_trading_enabled = False
                self.auto_trader_checkbox.setChecked(False)
                return False

            # Check minimum confidence threshold
            confidence = verdict_data.get('confidence', 0)
            if confidence < 70:  # Minimum 70% confidence for autonomous trades
                self.log_message(f"❌ Confidence too low ({confidence}%) for autonomous trade. Minimum 70% required.")
                return False

            # Check account balance
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)
            if free_balance < 20:  # Minimum $20 for autonomous trades
                self.log_message(f"❌ Insufficient balance (${free_balance:.2f}) for autonomous trade. Minimum $20 required.")
                return False

            # Check for over-concentration
            if not self.check_position_concentration(account_state, verdict_data):
                return False

            # Check market conditions
            if not self.check_market_conditions_for_trading(account_state):
                return False

            self.log_message("✅ All pre-execution safety checks passed")
            return True

        except Exception as e:
            self.log_message(f"Error in pre-execution safety checks: {str(e)}")
            return False

    def calculate_intelligent_position_size(self, verdict_data, account_state):
        """Calculate intelligent position size based on account state and risk management"""
        try:
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)
            confidence = verdict_data.get('confidence', 0)

            # Base position size (2% of account balance)
            base_risk_pct = 2.0
            base_position_value = free_balance * (base_risk_pct / 100)

            # Adjust based on confidence (70-100% confidence maps to 0.5x-1.5x multiplier)
            confidence_multiplier = 0.5 + ((confidence - 70) / 30) * 1.0
            confidence_multiplier = max(0.5, min(1.5, confidence_multiplier))

            # Adjust based on current drawdown
            risk_metrics = account_state.get('risk_metrics', {})
            max_drawdown = risk_metrics.get('max_drawdown', 0)
            if max_drawdown > 10:  # Reduce size if significant drawdown
                drawdown_multiplier = max(0.3, 1.0 - (max_drawdown / 100))
            else:
                drawdown_multiplier = 1.0

            # Adjust based on open positions (reduce if many positions)
            open_positions = account_state.get('open_positions', [])
            position_count_multiplier = max(0.5, 1.0 - (len(open_positions) * 0.1))

            # Calculate final position size
            final_position_value = base_position_value * confidence_multiplier * drawdown_multiplier * position_count_multiplier

            # Ensure minimum and maximum limits
            min_position = 5.0  # Minimum $5
            max_position = free_balance * 0.1  # Maximum 10% of balance

            final_position_value = max(min_position, min(max_position, final_position_value))

            self.log_message(f"🧮 Position Size Calculation:")
            self.log_message(f"   Base (2% of ${free_balance:.2f}): ${base_position_value:.2f}")
            self.log_message(f"   Confidence multiplier ({confidence}%): {confidence_multiplier:.2f}x")
            self.log_message(f"   Drawdown multiplier: {drawdown_multiplier:.2f}x")
            self.log_message(f"   Position count multiplier: {position_count_multiplier:.2f}x")
            self.log_message(f"   Final position size: ${final_position_value:.2f}")

            return final_position_value

        except Exception as e:
            self.log_message(f"Error calculating intelligent position size: {str(e)}")
            return 0

    def check_position_concentration(self, account_state, verdict_data):
        """Check for over-concentration in positions"""
        try:
            symbol = verdict_data.get('symbol', self.symbol_combo.currentText())
            open_positions = account_state.get('open_positions', [])

            # Check if already have position in same symbol
            existing_position = None
            for pos in open_positions:
                if pos['symbol'] == symbol:
                    existing_position = pos
                    break

            if existing_position:
                self.log_message(f"⚠️ Already have position in {symbol}: {existing_position['side']} {existing_position['size']}")
                # Allow if it's the same direction, reject if opposite
                verdict = verdict_data.get('verdict', 'WAIT')
                if (verdict == "LONG" and existing_position['side'] == 'short') or \
                   (verdict == "SHORT" and existing_position['side'] == 'long'):
                    self.log_message(f"❌ Cannot open {verdict} position - conflicts with existing {existing_position['side']} position")
                    return False

            # Check total exposure
            total_exposure = sum(abs(pos['size'] * pos['entry_price']) for pos in open_positions)
            free_balance = account_state.get('balance_info', {}).get('free_balance', 0)

            if total_exposure > free_balance * 0.8:  # Max 80% exposure
                self.log_message(f"❌ Total exposure too high: ${total_exposure:.2f} (max ${free_balance * 0.8:.2f})")
                return False

            return True

        except Exception as e:
            self.log_message(f"Error checking position concentration: {str(e)}")
            return False

    def check_market_conditions_for_trading(self, account_state):
        """Check if market conditions are suitable for trading"""
        try:
            market_conditions = account_state.get('market_conditions', {})

            # Check volatility (too high = dangerous)
            volatility = market_conditions.get('volatility', 0)
            if volatility > 5.0:  # 5% volatility threshold
                self.log_message(f"⚠️ High volatility ({volatility:.2f}%) - reducing trade aggressiveness")

            # Check liquidity
            liquidity_score = market_conditions.get('liquidity_score', 0)
            if liquidity_score < 0.3:  # Minimum liquidity threshold
                self.log_message(f"❌ Low liquidity ({liquidity_score:.2f}) - trade cancelled for safety")
                return False

            # Check spread
            spread = market_conditions.get('spread', 0)
            if spread > 0.5:  # 0.5% spread threshold
                self.log_message(f"⚠️ Wide spread ({spread:.3f}%) - may impact execution")

            return True

        except Exception as e:
            self.log_message(f"Error checking market conditions: {str(e)}")
            return True  # Default to allow trading if check fails

    def log_autonomous_decision_context(self, verdict_data, account_state, position_size):
        """Log comprehensive decision context for transparency"""
        try:
            self.log_message("🧠 AUTONOMOUS DECISION CONTEXT:")
            self.log_message(f"   💰 Account Balance: ${account_state.get('balance_info', {}).get('free_balance', 0):.2f}")
            self.log_message(f"   📊 Open Positions: {len(account_state.get('open_positions', []))}")
            self.log_message(f"   📈 Win Rate: {account_state.get('risk_metrics', {}).get('win_rate', 0):.1f}%")
            self.log_message(f"   📉 Max Drawdown: {account_state.get('risk_metrics', {}).get('max_drawdown', 0):.1f}%")
            self.log_message(f"   🎯 Confidence: {verdict_data.get('confidence', 0):.1f}%")
            self.log_message(f"   💵 Position Size: ${position_size:.2f}")
            self.log_message(f"   ⚖️ Leverage: {self.leverage_spinbox.value()}x")
            self.log_message(f"   🔄 Daily Trades: {self.autonomous_trading_stats['daily_trades']}/{self.autonomous_trading_stats['max_daily_trades']}")

        except Exception as e:
            self.log_message(f"Error logging decision context: {str(e)}")

    def execute_autonomous_long(self, symbol, position_size, verdict_data):
        """Execute autonomous long position"""
        try:
            leverage = self.leverage_spinbox.value()

            self.log_message(f"🤖 EXECUTING AUTONOMOUS LONG: {position_size:.2f} {symbol} @ {leverage}x leverage")

            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_long(symbol, position_size, leverage)
                if success:
                    self.log_message(f"✅ Autonomous LONG executed: ${position_size:.2f} {symbol}")
                    return True
                else:
                    self.log_message(f"❌ Failed to execute autonomous LONG")
                    return False
            else:
                self.log_message("❌ Real trading interface not available")
                return False

        except Exception as e:
            self.log_message(f"Error executing autonomous long: {str(e)}")
            return False

    def execute_autonomous_short(self, symbol, position_size, verdict_data):
        """Execute autonomous short position"""
        try:
            leverage = self.leverage_spinbox.value()

            self.log_message(f"🤖 EXECUTING AUTONOMOUS SHORT: {position_size:.2f} {symbol} @ {leverage}x leverage")

            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_short(symbol, position_size, leverage)
                if success:
                    self.log_message(f"✅ Autonomous SHORT executed: ${position_size:.2f} {symbol}")
                    return True
                else:
                    self.log_message(f"❌ Failed to execute autonomous SHORT")
                    return False
            else:
                self.log_message("❌ Real trading interface not available")
                return False

        except Exception as e:
            self.log_message(f"Error executing autonomous short: {str(e)}")
            return False

    def update_autonomous_trading_stats(self, success, verdict_data):
        """Update autonomous trading statistics"""
        try:
            from datetime import datetime

            self.autonomous_trading_stats['total_trades'] += 1
            self.autonomous_trading_stats['daily_trades'] += 1
            self.autonomous_trading_stats['last_trade_time'] = datetime.now()

            if success:
                self.autonomous_trading_stats['successful_trades'] += 1

            # Check for emergency stop conditions
            self.check_emergency_stop_conditions()

        except Exception as e:
            self.log_message(f"Error updating autonomous trading stats: {str(e)}")

    def check_emergency_stop_conditions(self):
        """Check if emergency stop should be triggered"""
        try:
            # Check max drawdown
            risk_metrics = self.calculate_risk_metrics()
            max_drawdown = risk_metrics.get('max_drawdown', 0)

            if max_drawdown > self.autonomous_trading_stats['max_drawdown_limit']:
                self.trigger_emergency_stop(f"Max drawdown exceeded: {max_drawdown:.1f}%")
                return

            # Check daily trade limit
            if self.autonomous_trading_stats['daily_trades'] >= self.autonomous_trading_stats['max_daily_trades']:
                self.log_message(f"⚠️ Daily trade limit reached. Disabling autonomous trading.")
                self.autonomous_trading_enabled = False
                self.auto_trader_checkbox.setChecked(False)
                return

        except Exception as e:
            self.log_message(f"Error checking emergency stop conditions: {str(e)}")

    def trigger_emergency_stop(self, reason):
        """Trigger emergency stop for autonomous trading"""
        try:
            self.autonomous_trading_stats['emergency_stop_triggered'] = True
            self.autonomous_trading_enabled = False
            self.auto_trader_checkbox.setChecked(False)

            self.log_message("🚨 ═══════════════════════════════════════════════════════════════")
            self.log_message("🚨 EMERGENCY STOP TRIGGERED")
            self.log_message("🚨 ═══════════════════════════════════════════════════════════════")
            self.log_message(f"🚨 Reason: {reason}")
            self.log_message("🚨 Autonomous trading has been disabled")
            self.log_message("🚨 Manual intervention required")

        except Exception as e:
            self.log_message(f"Error triggering emergency stop: {str(e)}")

    def update_chart(self):
        """Update chart with latest data (fallback chart only)"""
        try:
            # Only update if using fallback chart
            if not hasattr(self, 'chart_widget'):
                return

            symbol = self.symbol_combo.currentText()

            # Check if we have timeframe combo (fallback chart)
            if hasattr(self, 'timeframe_combo'):
                timeframe = self.timeframe_combo.currentText()
            else:
                timeframe = "1m"  # Default

            # Check if we have chart type combo (fallback chart)
            if hasattr(self, 'chart_type_combo'):
                chart_type = self.chart_type_combo.currentText()
            else:
                chart_type = "Line"  # Default

            # Fetch OHLCV data
            ohlcv_data = fetch_ohlcv(symbol, timeframe, 100)

            if not ohlcv_data:
                return

            # Clear previous data
            self.chart_widget.clear()

            # Extract data
            timestamps = [item[0] / 1000 for item in ohlcv_data]  # Convert to seconds
            prices = [item[4] for item in ohlcv_data]  # Close prices

            if chart_type == "Line":
                # Plot line chart
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=2)
                self.chart_widget.plot(timestamps, prices, pen=pen)
            else:
                # Plot candlestick chart (simplified as line for now)
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=1)
                self.chart_widget.plot(timestamps, prices, pen=pen)

            # Auto-scale to fit the actual data properly
            self.chart_widget.getPlotItem().getViewBox().autoRange()
            self.chart_widget.getPlotItem().getViewBox().enableAutoRange(axis='y', enable=True)

            # Update best bid/ask display
            self.update_bid_ask_display()

        except Exception as e:
            print(f"Error updating chart: {e}")

    def update_bid_ask_display(self):
        """Update best bid/ask display - ONLY called for fallback chart, WebSocket handles live updates"""
        try:
            # This method is now only used for fallback scenarios
            # Live WebSocket data is handled directly in on_live_orderbook_updated()
            # Only update if we don't have current bid/ask from WebSocket
            if not hasattr(self, 'current_bid') or self.current_bid is None:
                symbol = self.symbol_combo.currentText()

                # Use real trading interface if available
                if hasattr(self, 'real_trading') and self.real_trading:
                    best_bid, best_ask = self.real_trading.get_best_bid_ask(symbol)

                    if best_bid is not None:
                        self.current_bid = best_bid
                    if best_ask is not None:
                        self.current_ask = best_ask

            # Update display with current values (from WebSocket or fallback)
            if hasattr(self, 'best_bid_label') and hasattr(self, 'best_ask_label'):
                if self.current_bid is not None:
                    bid_text = f"{self.current_bid:.6f}"
                    if hasattr(self, 'last_bid') and self.last_bid is not None:
                        if self.current_bid > self.last_bid:
                            bid_text += " ↑"
                        elif self.current_bid < self.last_bid:
                            bid_text += " ↓"
                    self.best_bid_label.setText(bid_text)
                else:
                    self.best_bid_label.setText("--")

                if self.current_ask is not None:
                    ask_text = f"{self.current_ask:.6f}"
                    if hasattr(self, 'last_ask') and self.last_ask is not None:
                        if self.current_ask > self.last_ask:
                            ask_text += " ↑"
                        elif self.current_ask < self.last_ask:
                            ask_text += " ↓"
                    self.best_ask_label.setText(ask_text)
                else:
                    self.best_ask_label.setText("--")

                # Calculate and display spread
                if self.current_bid is not None and self.current_ask is not None:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                    self.spread_label.setText(f"{spread:.6f} ({spread_pct:.3f}%)")
                else:
                    self.spread_label.setText("--")

        except Exception as e:
            print(f"Error updating bid/ask display: {e}")
            if hasattr(self, 'best_bid_label'):
                self.best_bid_label.setText("--")
            if hasattr(self, 'best_ask_label'):
                self.best_ask_label.setText("--")
            if hasattr(self, 'spread_label'):
                self.spread_label.setText("--")

    def _set_trading_buttons_enabled(self, enabled: bool):
        """Enable/disable trading buttons (for loading states)"""
        try:
            self.limit_long_btn.setEnabled(enabled)
            self.market_long_btn.setEnabled(enabled)
            self.limit_short_btn.setEnabled(enabled)
            self.market_short_btn.setEnabled(enabled)
            self.close_all_btn.setEnabled(enabled)
            self.cancel_all_btn.setEnabled(enabled)

            # Update button text to show loading state
            if not enabled:
                buttons = [
                    (self.limit_long_btn, "PLACING..."),
                    (self.market_long_btn, "PLACING..."),
                    (self.limit_short_btn, "PLACING..."),
                    (self.market_short_btn, "PLACING..."),
                    (self.close_all_btn, "CLOSING..."),
                    (self.cancel_all_btn, "CANCELLING...")
                ]
                for btn, text in buttons:
                    btn.setText(text)
            else:
                # Restore original button text
                self.limit_long_btn.setText("LIMIT LONG")
                self.market_long_btn.setText("MARKET LONG")
                self.limit_short_btn.setText("LIMIT SHORT")
                self.market_short_btn.setText("MARKET SHORT")
                self.close_all_btn.setText("CLOSE ALL")
                self.cancel_all_btn.setText("CANCEL ALL")

        except Exception as e:
            print(f"Error setting button states: {e}")

    def on_chart_click(self, event):
        """Handle chart click events for order placement using best bid/ask"""
        try:
            if event.button() == 1:  # Left click - BUY (use best bid)
                self.place_limit_long()
            elif event.button() == 2:  # Right click - SELL (use best ask)
                self.place_limit_short()
            else:
                return

        except Exception as e:
            print(f"Error handling chart click: {e}")

    def on_live_chart_click(self, price: float, timestamp: float):
        """Handle live chart click events"""
        try:
            # For now, just trigger the same order placement logic
            # In the future, could use the clicked price for limit orders
            print(f"Live chart clicked at price: {price:.6f}, time: {timestamp}")

            # Use current best bid/ask instead of clicked price for safety
            self.place_limit_long()  # Default to long, could add right-click detection

        except Exception as e:
            print(f"Error handling live chart click: {e}")

    def on_chart_symbol_changed(self, symbol: str):
        """Handle symbol change from chart"""
        try:
            # Update main symbol selector
            self.symbol_combo.setCurrentText(symbol)

            # Subscribe to new symbol in live data manager
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                self.live_data_manager.subscribe_symbol(symbol, ["1m", "5m", "15m"])

            self.log_message(f"Chart symbol changed to: {symbol}")

        except Exception as e:
            print(f"Error handling chart symbol change: {e}")

    def on_chart_timeframe_changed(self, timeframe: str):
        """Handle timeframe change from chart"""
        try:
            self.log_message(f"Chart timeframe changed to: {timeframe}")

        except Exception as e:
            print(f"Error handling chart timeframe change: {e}")

    def on_live_chart_data_updated(self, symbol: str, chart_data: dict):
        """Handle live chart data updates"""
        try:
            if hasattr(self, 'live_chart'):
                # Update the live chart widget with new data
                ohlcv_data = chart_data.get("ohlcv", [])
                if ohlcv_data:
                    self.live_chart.update_ohlcv_data(ohlcv_data)

        except Exception as e:
            print(f"Error handling live chart data update: {e}")

    def on_live_price_updated(self, symbol: str, price: float):
        """Handle live price updates"""
        try:
            # Update current price display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                # Update any price displays
                pass

        except Exception as e:
            print(f"Error handling live price update: {e}")

    def on_live_orderbook_updated(self, symbol: str, orderbook_data: dict):
        """Handle live order book updates with enhanced manual trading panel integration"""
        try:
            # Update bid/ask display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                bids = orderbook_data.get("bids", [])
                asks = orderbook_data.get("asks", [])

                best_bid = bids[0][0] if bids else None
                best_ask = asks[0][0] if asks else None

                # Store the bid/ask values for manual trading
                if best_bid is not None:
                    self.last_bid = getattr(self, 'current_bid', None)
                    self.current_bid = best_bid
                if best_ask is not None:
                    self.last_ask = getattr(self, 'current_ask', None)
                    self.current_ask = best_ask

                # Update live chart bid/ask display
                if hasattr(self, 'live_chart'):
                    self.live_chart.update_bid_ask_data(best_bid, best_ask)

                # Update real trading interface with live orderbook data (if method exists)
                if hasattr(self, 'real_trading') and self.real_trading and hasattr(self.real_trading, 'update_live_orderbook'):
                    self.real_trading.update_live_orderbook(symbol, orderbook_data)

                # Update prediction tracker with current price
                if hasattr(self, 'prediction_tracker') and best_bid is not None:
                    self.prediction_tracker.update_current_price(symbol, best_bid)

                # Update manual trading panel with live WebSocket data ONLY
                if hasattr(self, 'best_bid_label') and hasattr(self, 'best_ask_label'):
                    # Update stored values for movement tracking
                    if best_bid is not None:
                        self.last_bid = getattr(self, 'current_bid', None)
                        self.current_bid = best_bid
                    if best_ask is not None:
                        self.last_ask = getattr(self, 'current_ask', None)
                        self.current_ask = best_ask

                    # Update bid display with movement indicator using batched updates
                    if self.current_bid is not None:
                        bid_text = f"{self.current_bid:.6f}"
                        if self.last_bid is not None:
                            if self.current_bid > self.last_bid:
                                bid_text += " ↑"
                            elif self.current_bid < self.last_bid:
                                bid_text += " ↓"
                        self.batch_gui_update('best_bid_label', 'text', bid_text)

                    # Update ask display with movement indicator using batched updates
                    if self.current_ask is not None:
                        ask_text = f"{self.current_ask:.6f}"
                        if self.last_ask is not None:
                            if self.current_ask > self.last_ask:
                                ask_text += " ↑"
                            elif self.current_ask < self.last_ask:
                                ask_text += " ↓"
                        self.batch_gui_update('best_ask_label', 'text', ask_text)

                    # Calculate and display spread using batched updates
                    if self.current_bid is not None and self.current_ask is not None:
                        spread = self.current_ask - self.current_bid
                        spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                        self.batch_gui_update('spread_label', 'text', f"{spread:.6f} ({spread_pct:.3f}%)")

                # Auto-update price spinbox if it's currently at default or old values
                if hasattr(self, 'price_spinbox') and best_bid and best_ask:
                    current_price = self.price_spinbox.value()
                    # If price is at default (0.175) or very different from market, suggest update
                    if current_price == 0.175 or abs(current_price - best_bid) / best_bid > 0.1:
                        # Set to mid-price as a reasonable default
                        mid_price = (best_bid + best_ask) / 2
                        self.price_spinbox.setValue(mid_price)

        except Exception as e:
            print(f"Error handling live orderbook update: {e}")

    def on_live_connection_status(self, connected: bool):
        """Handle live data connection status changes"""
        try:
            # Update live chart connection status
            if hasattr(self, 'live_chart'):
                self.live_chart.update_connection_status(connected)

            # Update status in log
            status = "Connected to live data" if connected else "Disconnected from live data"
            self.log_message(status)

        except Exception as e:
            print(f"Error handling live connection status: {e}")

    def on_trade_update(self, symbol: str, trade_data: dict):
        """Handle individual trade updates from WebSocket"""
        try:
            # Only process trades for the active chart symbol
            if hasattr(self, 'live_chart') and symbol == self.live_chart.current_symbol:
                trades = trade_data.get('trades', [])

                for trade in trades:
                    price = float(trade.get('price', 0))
                    amount = float(trade.get('amount', 0))
                    direction = trade.get('direction', 'buy')  # HTX uses 'buy'/'sell'
                    timestamp = float(trade.get('ts', 0)) / 1000  # Convert to seconds

                    # Update chart with individual trade
                    self.live_chart.update_trade_data(price, amount, direction, timestamp)

        except Exception as e:
            print(f"Error handling trade update: {e}")

    # Real Trading Interface Signal Handlers
    def on_order_status_updated(self, order_info: dict):
        """Handle order status updates from real trading interface"""
        try:
            order_type = order_info.get('type', '')
            order = order_info.get('order', {})

            if order_type == 'order_placed':
                self.log_message(f"Order placed: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')}")
            elif order_type == 'order_filled':
                self.log_message(f"Order filled: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')} @ {order.get('price', 0)}")
            elif order_type == 'order_cancelled':
                self.log_message(f"Order cancelled: {order.get('id', 'N/A')}")

        except Exception as e:
            print(f"Error handling order status update: {e}")

    def on_position_status_updated(self, position_info: dict):
        """Handle position status updates from real trading interface"""
        try:
            symbol = position_info.get('symbol', '')
            size = position_info.get('size', 0)
            side = position_info.get('side', '')
            pnl = position_info.get('unrealized_pnl', 0)

            if size != 0:
                self.log_message(f"Position update: {side} {abs(size)} {symbol} - PnL: ${pnl:.2f}")

        except Exception as e:
            print(f"Error handling position status update: {e}")

    def on_balance_status_updated(self, balance_info: dict):
        """Handle balance status updates from real trading interface"""
        try:
            usdt_info = balance_info.get('USDT', {})
            free_balance = usdt_info.get('free', 0)
            total_balance = usdt_info.get('total', 0)

            # Update menu bar balance display (like in your image)
            if hasattr(self, 'balance_label'):
                balance_text = f"Equity: ${total_balance:.2f} Free: ${free_balance:.2f}"
                self.balance_label.setText(balance_text)

                # Color coding based on balance
                if free_balance > 0:
                    color = MatrixTheme.YELLOW  # Yellow for positive balance
                else:
                    color = MatrixTheme.RED  # Red for zero/negative balance

                self.balance_label.setStyleSheet(f"""
                    color: {color};
                    font-weight: bold;
                    font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
                    padding: 2px 6px;
                    border: 1px solid {color};
                    border-radius: 3px;
                    background-color: rgba(255, 255, 0, 0.1);
                """)

            # Log balance update (less verbose)
            # self.log_message(f"💰 Balance: ${free_balance:.2f} USDT")

        except Exception as e:
            print(f"Error handling balance status update: {e}")

    def on_trading_error(self, error_message: str):
        """Handle trading errors from real trading interface"""
        try:
            self.log_message(f"Trading Error: {error_message}")
            self.statusBar().showMessage(f"Trading Error: {error_message}", 5000)

        except Exception as e:
            print(f"Error handling trading error: {e}")

    def on_trading_status(self, status_message: str):
        """Handle trading status updates from real trading interface"""
        try:
            self.log_message(f"Trading Status: {status_message}")

        except Exception as e:
            print(f"Error handling trading status: {e}")

    def on_pnl_updated(self, pnl_summary: dict):
        """Handle PnL updates from real trading interface"""
        try:
            unrealized = pnl_summary.get('unrealized_pnl', 0)
            realized = pnl_summary.get('realized_pnl', 0)
            total = pnl_summary.get('total_pnl', 0)

            # Update PnL display (could add to UI later)
            if total != 0:
                self.log_message(f"PnL Update: Total ${total:.2f} (Unrealized: ${unrealized:.2f}, Realized: ${realized:.2f})")

        except Exception as e:
            print(f"Error handling PnL update: {e}")

    def on_risk_warning(self, warning_type: str, warning_message: str):
        """Handle risk warnings from real trading interface"""
        try:
            self.log_message(f"RISK WARNING [{warning_type.upper()}]: {warning_message}")
            self.statusBar().showMessage(f"RISK WARNING: {warning_message}", 10000)

        except Exception as e:
            print(f"Error handling risk warning: {e}")

    # Signal Trading Engine Signal Handlers
    def on_signal_received(self, signal_data: dict):
        """Handle signal received from signal trading engine"""
        try:
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            confidence = signal_data.get('confidence', 0)

            self.log_message(f"Signal received: {source} -> {decision} ({confidence:.1%})")

            # Record signal in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_signal(signal_data)

        except Exception as e:
            print(f"Error handling signal received: {e}")

    def on_trade_decision_made(self, decision_data: dict):
        """Handle trade decision from signal trading engine"""
        try:
            symbol = decision_data.get('symbol', '')
            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0)
            reasoning = decision_data.get('reasoning', '')

            self.log_message(f"Trade decision: {decision} {symbol} ({confidence:.1%}) - {reasoning}")

        except Exception as e:
            print(f"Error handling trade decision: {e}")

    def on_automated_trade_executed(self, trade_data: dict):
        """Handle automated trade execution"""
        try:
            symbol = trade_data.get('symbol', '')
            decision = trade_data.get('decision', '')
            position_size = trade_data.get('position_size', 0)
            confidence = trade_data.get('confidence', 0)

            self.log_message(f"🤖 Automated trade: {decision} {position_size} {symbol} (confidence: {confidence:.1%})")
            self.statusBar().showMessage(f"Automated {decision} executed", 5000)

            # Record trade in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_trade(trade_data)

        except Exception as e:
            print(f"Error handling automated trade: {e}")

    def on_risk_limit_triggered(self, limit_type: str, details: dict):
        """Handle risk limit triggered"""
        try:
            self.log_message(f"🚨 RISK LIMIT TRIGGERED: {limit_type} - {details}")
            self.statusBar().showMessage(f"RISK LIMIT: {limit_type}", 10000)

        except Exception as e:
            print(f"Error handling risk limit: {e}")

    def on_engine_status_changed(self, status: str):
        """Handle signal trading engine status changes"""
        try:
            self.log_message(f"Engine status: {status}")

        except Exception as e:
            print(f"Error handling engine status: {e}")

    # Session Management Signal Handlers
    def on_session_started(self, session_id: str):
        """Handle session started"""
        try:
            self.log_message(f"📊 Session started: {session_id}")

        except Exception as e:
            print(f"Error handling session started: {e}")

    def on_session_ended(self, session_id: str, summary: dict):
        """Handle session ended"""
        try:
            trade_stats = summary.get('trade_stats', {})
            total_trades = trade_stats.get('total_trades', 0)
            win_rate = trade_stats.get('win_rate', 0)
            total_pnl = trade_stats.get('total_pnl', 0)

            self.log_message(f"📊 Session ended: {session_id}")
            self.log_message(f"📈 Summary: {total_trades} trades, {win_rate:.1f}% win rate, ${total_pnl:.2f} PnL")

        except Exception as e:
            print(f"Error handling session ended: {e}")

    def on_trade_recorded_to_db(self, trade_data: dict):
        """Handle trade recorded to database"""
        try:
            trade_id = trade_data.get('trade_id', 'N/A')
            pnl = trade_data.get('pnl', 0)
            self.log_message(f"💾 Trade recorded: {trade_id} (PnL: ${pnl:.2f})")

        except Exception as e:
            print(f"Error handling trade recorded: {e}")

    def on_signal_recorded_to_db(self, signal_data: dict):
        """Handle signal recorded to database"""
        try:
            # Signal data available for future processing
            if signal_data:  # Use signal_data to avoid unused parameter warning
                # source = signal_data.get('source', 'unknown')
                # decision = signal_data.get('decision', 'WAIT')
                # Log at debug level to avoid spam
                # self.log_message(f"💾 Signal recorded: {source} -> {decision}")
                pass

        except Exception as e:
            print(f"Error handling signal recorded: {e}")

def main():
    """Main application entry point"""
    try:
        print("Starting Epinnox v6 Trading System...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxTradingInterface()
        window.show()
        
        print("✓ Epinnox v6 GUI started successfully")
        print("✓ Ready for trading analysis")
        
        # Run application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ Error starting Epinnox v6: {e}")
        print("\nMake sure you have:")
        print("1. PyQt5 installed: pip install PyQt5")
        print("2. Running from the Epinnox_v6 directory")
        sys.exit(1)

if __name__ == "__main__":
    main()