"""
CCXT Trading Engine for Epinnox v6
Real exchange API integration with comprehensive error handling and safety measures
"""

import ccxt
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import traceback


class CCXTTradingEngine(QObject):
    """
    Real trading engine using CCXT for exchange integration
    Handles order placement, position tracking, and risk management
    """
    
    # Signals for UI updates
    order_placed = pyqtSignal(dict)  # order_info
    order_filled = pyqtSignal(dict)  # order_info
    order_cancelled = pyqtSignal(dict)  # order_info
    position_updated = pyqtSignal(dict)  # position_info
    balance_updated = pyqtSignal(dict)  # balance_info
    error_occurred = pyqtSignal(str)  # error_message
    status_updated = pyqtSignal(str)  # status_message
    
    def __init__(self, exchange_name="htx", demo_mode=True):
        super().__init__()
        
        self.exchange_name = exchange_name
        self.demo_mode = demo_mode
        self.exchange = None
        self.is_connected = False
        
        # Trading state
        self.open_orders = {}
        self.open_positions = {}
        self.account_balance = {}
        self.trading_fees = {}
        
        # Risk management settings
        self.max_position_size = 1000.0  # Maximum position size in USD
        self.max_daily_loss = 100.0      # Maximum daily loss in USD
        self.min_balance_threshold = 10.0  # Minimum balance to maintain
        self.daily_pnl = 0.0
        self.daily_reset_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Update timers
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_positions)
        self.position_timer.start(5000)  # Update every 5 seconds
        
        self.balance_timer = QTimer()
        self.balance_timer.timeout.connect(self.update_balance)
        self.balance_timer.start(10000)  # Update every 10 seconds
        
        # Initialize exchange
        self.initialize_exchange()
    
    def initialize_exchange(self):
        """Initialize CCXT exchange connection"""
        try:
            if self.demo_mode:
                self.status_updated.emit("Initializing in DEMO mode")
                # Create exchange instance without credentials for demo
                if self.exchange_name == "htx":
                    self.exchange = ccxt.htx({
                        'enableRateLimit': True,
                        'sandbox': False,  # HTX doesn't have sandbox
                    })
                else:
                    self.exchange = getattr(ccxt, self.exchange_name)({
                        'enableRateLimit': True,
                        'sandbox': True,
                    })
                
                self.is_connected = True
                self.status_updated.emit("DEMO mode initialized")
                return True
            
            else:
                # Load real API credentials
                print(f"🔑 CCXTTradingEngine: Loading credentials for {self.exchange_name}")
                credentials = self.load_api_credentials()
                if not credentials:
                    self.error_occurred.emit("No API credentials found. Switching to DEMO mode.")
                    print("❌ CCXTTradingEngine: No credentials found, switching to DEMO mode")
                    self.demo_mode = True
                    return self.initialize_exchange()

                print(f"✅ CCXTTradingEngine: Credentials loaded successfully")
                print(f"   API Key: {credentials['api_key'][:8]}...{credentials['api_key'][-4:]}")
                print(f"   Sandbox: {'ON' if credentials.get('sandbox', False) else 'OFF'}")
                
                # Initialize with real credentials
                if self.exchange_name == "htx":
                    self.exchange = ccxt.htx({
                        'apiKey': credentials['api_key'],
                        'secret': credentials['secret'],
                        'password': credentials.get('passphrase', ''),
                        'enableRateLimit': True,
                        'sandbox': credentials.get('sandbox', False),
                        'timeout': 30000,
                        'rateLimit': 100,
                        'options': {
                            'defaultType': 'swap',  # HTX Linear Swaps
                            'marginMode': 'cross',  # Cross margin mode
                            'fetchCurrencies': False,  # Disable problematic endpoint
                        },
                        'urls': {
                            'api': {
                                'swap': 'https://api.hbdm.com',  # HTX Linear Swap API
                            }
                        }
                    })
                    print(f"🔧 HTX configured for LINEAR SWAP trading")
                    print(f"   Default Type: swap")
                    print(f"   Margin Mode: cross")
                    print(f"   API Endpoint: https://api.hbdm.com")
                else:
                    # For other exchanges
                    exchange_class = getattr(ccxt, self.exchange_name)
                    self.exchange = exchange_class({
                        'apiKey': credentials['api_key'],
                        'secret': credentials['secret'],
                        'password': credentials.get('passphrase', ''),
                        'enableRateLimit': True,
                        'sandbox': credentials.get('sandbox', False),
                        'timeout': 30000,
                        'rateLimit': 100,
                    })
                
                # Test connection
                self.exchange.load_markets()
                self.is_connected = True
                self.status_updated.emit("Connected to live exchange")
                return True
                
        except Exception as e:
            self.error_occurred.emit(f"Exchange initialization failed: {str(e)}")
            self.demo_mode = True
            self.is_connected = False
            return False
    
    def load_api_credentials(self) -> Optional[Dict]:
        """Load API credentials from config file"""
        try:
            # Use the same credential loading system as the main application
            from config.production_loader import get_live_trading_credentials

            creds = get_live_trading_credentials()
            if creds and creds.get('apiKey') and creds.get('secret'):
                # Convert to the format expected by this class
                return {
                    'api_key': creds['apiKey'],
                    'secret': creds['secret'],
                    'passphrase': creds.get('password', ''),
                    'sandbox': creds.get('sandbox', False)
                }

            # Fallback: Try to load from legacy config file
            import os
            config_path = os.path.join("config", "api_credentials.json")

            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    credentials = json.load(f)
                    return credentials.get(self.exchange_name, {})

            # Fallback: Try environment variables
            api_key = os.getenv(f"{self.exchange_name.upper()}_API_KEY")
            secret = os.getenv(f"{self.exchange_name.upper()}_SECRET")

            if api_key and secret:
                return {
                    'api_key': api_key,
                    'secret': secret,
                    'passphrase': os.getenv(f"{self.exchange_name.upper()}_PASSPHRASE", '')
                }

            return None
            
        except Exception as e:
            self.error_occurred.emit(f"Error loading credentials: {str(e)}")
            return None
    
    def place_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict = None) -> Optional[Dict]:
        """Place a limit order"""
        try:
            if params is None:
                params = {}

            print(f"🔄 Placing LIMIT order: {side.upper()} {amount} {symbol} @ ${price}")

            # Validate inputs
            if not self.validate_order_inputs(symbol, side, amount, price):
                print(f"❌ Order validation failed")
                return None
            
            # Check risk limits
            if not self.check_risk_limits(symbol, side, amount, price):
                return None
            
            if self.demo_mode:
                # Simulate order in demo mode
                order = self.simulate_limit_order(symbol, side, amount, price, params)
            else:
                # Place real order on HTX Linear Swaps
                if self.exchange_name == "htx":
                    # HTX Linear Swap specific parameters
                    htx_params = {
                        'type': 'swap',  # Linear swap
                        'marginMode': 'cross',  # Cross margin
                        **params
                    }
                    print(f"🔧 HTX Linear Swap order params: {htx_params}")
                    order = self.exchange.create_limit_order(symbol, side, amount, price, htx_params)
                else:
                    order = self.exchange.create_limit_order(symbol, side, amount, price, params)

                # Store order
                self.open_orders[order['id']] = order
                print(f"📝 Order stored: {order['id']}")
            
            self.order_placed.emit(order)
            success_msg = f"Limit {side.upper()} order placed: {amount} {symbol} @ {price}"
            print(f"✅ {success_msg}")
            self.status_updated.emit(success_msg)

            return order
            
        except Exception as e:
            error_msg = f"Error placing limit order: {str(e)}"
            self.error_occurred.emit(error_msg)
            print(f"Limit order error: {traceback.format_exc()}")
            return None
    
    def place_market_order(self, symbol: str, side: str, amount: float, params: Dict = None) -> Optional[Dict]:
        """Place a market order"""
        try:
            if params is None:
                params = {}

            print(f"🔄 Placing MARKET order: {side.upper()} {amount} {symbol}")

            # Get current market price for validation
            ticker = self.get_ticker(symbol)
            if not ticker:
                error_msg = f"Could not get market price for {symbol}"
                print(f"❌ {error_msg}")
                self.error_occurred.emit(error_msg)
                return None
            
            current_price = ticker['last']
            
            # Validate inputs
            if not self.validate_order_inputs(symbol, side, amount, current_price):
                return None
            
            # Check risk limits
            if not self.check_risk_limits(symbol, side, amount, current_price):
                return None
            
            if self.demo_mode:
                # Simulate order in demo mode
                order = self.simulate_market_order(symbol, side, amount, params)
            else:
                # Place real market order on HTX Linear Swaps
                if self.exchange_name == "htx":
                    # HTX Linear Swap specific parameters
                    htx_params = {
                        'type': 'swap',  # Linear swap
                        'marginMode': 'cross',  # Cross margin
                        **params
                    }
                    print(f"🔧 HTX Linear Swap market order params: {htx_params}")
                    order = self.exchange.create_market_order(symbol, side, amount, htx_params)
                else:
                    order = self.exchange.create_market_order(symbol, side, amount, params)

                # Market orders are usually filled immediately
                self.order_filled.emit(order)
                print(f"📝 Market order executed: {order['id']}")
            
            self.order_placed.emit(order)
            success_msg = f"Market {side.upper()} order executed: {amount} {symbol}"
            print(f"✅ {success_msg}")
            self.status_updated.emit(success_msg)

            return order
            
        except Exception as e:
            error_msg = f"Error placing market order: {str(e)}"
            self.error_occurred.emit(error_msg)
            print(f"Market order error: {traceback.format_exc()}")
            return None
    
    def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an open order"""
        try:
            if self.demo_mode:
                # Simulate cancellation
                if order_id in self.open_orders:
                    order = self.open_orders[order_id]
                    order['status'] = 'canceled'
                    del self.open_orders[order_id]
                    self.order_cancelled.emit(order)
                    return True
                return False
            else:
                # Cancel real order
                result = self.exchange.cancel_order(order_id, symbol)
                
                if order_id in self.open_orders:
                    order = self.open_orders[order_id]
                    order['status'] = 'canceled'
                    del self.open_orders[order_id]
                    self.order_cancelled.emit(order)
                
                return True
                
        except Exception as e:
            self.error_occurred.emit(f"Error cancelling order: {str(e)}")
            return False
    
    def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all open orders for a symbol or all symbols"""
        try:
            cancelled_count = 0
            
            if self.demo_mode:
                # Cancel all demo orders
                orders_to_cancel = list(self.open_orders.keys())
                for order_id in orders_to_cancel:
                    order = self.open_orders[order_id]
                    if symbol is None or order['symbol'] == symbol:
                        if self.cancel_order(order_id, order['symbol']):
                            cancelled_count += 1
            else:
                # Cancel all real orders
                if symbol:
                    orders = self.exchange.fetch_open_orders(symbol)
                else:
                    orders = self.exchange.fetch_open_orders()
                
                for order in orders:
                    if self.cancel_order(order['id'], order['symbol']):
                        cancelled_count += 1
            
            self.status_updated.emit(f"Cancelled {cancelled_count} orders")
            return cancelled_count
            
        except Exception as e:
            self.error_occurred.emit(f"Error cancelling orders: {str(e)}")
            return 0
    
    def close_position(self, symbol: str, side: str = None) -> bool:
        """Close a position using market order"""
        try:
            # Get current position
            position = self.get_position(symbol)
            if not position or position['size'] == 0:
                self.status_updated.emit(f"No open position for {symbol}")
                return True
            
            # Determine close side
            position_side = 'long' if position['side'] == 'long' else 'short'
            close_side = 'sell' if position_side == 'long' else 'buy'
            
            # Close with market order
            close_params = {'reduceOnly': True}
            order = self.place_market_order(symbol, close_side, abs(position['size']), close_params)
            
            if order:
                self.status_updated.emit(f"Closed {position_side} position for {symbol}")
                return True
            
            return False
            
        except Exception as e:
            self.error_occurred.emit(f"Error closing position: {str(e)}")
            return False
    
    def close_all_positions(self) -> int:
        """Close all open positions"""
        try:
            closed_count = 0
            positions = self.get_all_positions()
            
            for symbol, position in positions.items():
                if position['size'] != 0:
                    if self.close_position(symbol):
                        closed_count += 1
            
            self.status_updated.emit(f"Closed {closed_count} positions")
            return closed_count
            
        except Exception as e:
            self.error_occurred.emit(f"Error closing positions: {str(e)}")
            return 0
    
    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for HTX Linear Swaps"""
        try:
            if self.demo_mode:
                self.status_updated.emit(f"DEMO: Set leverage {leverage}x for {symbol}")
                return True

            # Set real leverage for HTX Linear Swaps
            if self.exchange_name == "htx":
                print(f"🔧 Setting HTX Linear Swap leverage: {leverage}x for {symbol}")
                # HTX Linear Swap leverage setting
                result = self.exchange.set_leverage(leverage, symbol, {'type': 'swap'})
                print(f"✅ HTX leverage set successfully: {leverage}x")
            else:
                result = self.exchange.set_leverage(leverage, symbol)

            success_msg = f"Set leverage {leverage}x for {symbol}"
            print(f"✅ {success_msg}")
            self.status_updated.emit(success_msg)
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Error setting leverage: {str(e)}")
            return False

    # Data fetching methods
    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """Get current ticker data"""
        try:
            if self.demo_mode:
                # Return mock ticker data
                return {
                    'symbol': symbol,
                    'last': 0.35,  # Mock DOGE price
                    'bid': 0.349,
                    'ask': 0.351,
                    'high': 0.36,
                    'low': 0.34,
                    'volume': 1000000
                }

            return self.exchange.fetch_ticker(symbol)

        except Exception as e:
            self.error_occurred.emit(f"Error fetching ticker: {str(e)}")
            return None

    def get_orderbook(self, symbol: str) -> Optional[Dict]:
        """Get current order book"""
        try:
            if self.demo_mode:
                # Return mock order book
                return {
                    'symbol': symbol,
                    'bids': [[0.349, 1000], [0.348, 2000], [0.347, 1500]],
                    'asks': [[0.351, 1000], [0.352, 2000], [0.353, 1500]],
                    'timestamp': int(time.time() * 1000)
                }

            return self.exchange.fetch_order_book(symbol)

        except Exception as e:
            self.error_occurred.emit(f"Error fetching order book: {str(e)}")
            return None

    def get_position(self, symbol: str) -> Optional[Dict]:
        """Get current position for symbol"""
        try:
            if self.demo_mode:
                # Return mock position
                return self.open_positions.get(symbol, {
                    'symbol': symbol,
                    'size': 0,
                    'side': None,
                    'unrealizedPnl': 0,
                    'percentage': 0,
                    'entryPrice': 0,
                    'markPrice': 0
                })

            positions = self.exchange.fetch_positions([symbol])
            return positions[0] if positions else None

        except Exception as e:
            self.error_occurred.emit(f"Error fetching position: {str(e)}")
            return None

    def get_all_positions(self) -> Dict:
        """Get all open positions for HTX Linear Swaps"""
        try:
            if self.demo_mode:
                return self.open_positions

            # For HTX Linear Swaps, fetch positions with specific parameters
            if self.exchange_name == "htx":
                print(f"🔍 Fetching HTX Linear Swap positions...")
                positions = self.exchange.fetch_positions(None, {'type': 'swap'})
                print(f"📊 HTX positions response: {len(positions)} positions")
            else:
                positions = self.exchange.fetch_positions()

            # Filter positions with non-zero size, handling different field names
            filtered_positions = {}
            for pos in positions:
                try:
                    # HTX Linear Swaps use different field names
                    size = pos.get('size', pos.get('contracts', pos.get('amount', pos.get('contractSize', 0))))
                    if size != 0:
                        filtered_positions[pos['symbol']] = pos
                        print(f"📈 Active position: {pos['symbol']} - Size: {size}")
                except (KeyError, TypeError) as e:
                    print(f"⚠️ Position data format issue: {e}, position: {pos}")
                    continue

            print(f"✅ Found {len(filtered_positions)} active positions")
            return filtered_positions

        except Exception as e:
            error_msg = f"Error fetching positions: {str(e)}"
            print(f"❌ {error_msg}")  # Print to terminal
            self.error_occurred.emit(error_msg)
            return {}

    def get_balance(self) -> Optional[Dict]:
        """Get account balance for HTX Linear Swaps"""
        try:
            if self.demo_mode:
                # Return mock balance
                return {
                    'USDT': {'free': 1000.0, 'used': 0.0, 'total': 1000.0},
                    'total': {'USDT': 1000.0}
                }

            # For HTX Linear Swaps, we need to fetch swap balance specifically
            if self.exchange_name == "htx":
                print(f"🔍 Fetching HTX Linear Swap balance...")

                # HTX Linear Swap balance requires specific parameters
                balance = self.exchange.fetch_balance({'type': 'swap'})
                print(f"✅ HTX balance fetched: {balance}")
                return balance
            else:
                return self.exchange.fetch_balance()

        except Exception as e:
            error_msg = f"Error fetching balance: {str(e)}"
            print(f"❌ {error_msg}")  # Print to terminal
            self.error_occurred.emit(error_msg)
            return None

    # Validation and risk management
    def validate_order_inputs(self, symbol: str, side: str, amount: float, price: float) -> bool:
        """Validate order inputs"""
        try:
            # Basic validation
            if not symbol or not side:
                self.error_occurred.emit("Invalid symbol or side")
                return False

            if amount <= 0:
                self.error_occurred.emit("Amount must be positive")
                return False

            if price <= 0:
                self.error_occurred.emit("Price must be positive")
                return False

            if side not in ['buy', 'sell']:
                self.error_occurred.emit("Side must be 'buy' or 'sell'")
                return False

            # Check minimum order size
            if amount < 1.0:  # Minimum $1 order
                self.error_occurred.emit("Order size too small (minimum $1)")
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Validation error: {str(e)}")
            return False

    def check_risk_limits(self, symbol: str, side: str, amount: float, price: float) -> bool:
        """Check risk management limits"""
        try:
            # Calculate order value
            order_value = amount * price

            # Check maximum position size
            if order_value > self.max_position_size:
                self.error_occurred.emit(f"Order exceeds max position size (${self.max_position_size})")
                return False

            # Check daily loss limit
            if self.daily_pnl < -self.max_daily_loss:
                self.error_occurred.emit(f"Daily loss limit reached (${self.max_daily_loss})")
                return False

            # Check minimum balance
            balance = self.get_balance()
            if balance:
                usdt_balance = balance.get('USDT', {}).get('free', 0)
                if usdt_balance < self.min_balance_threshold:
                    self.error_occurred.emit(f"Insufficient balance (minimum ${self.min_balance_threshold})")
                    return False

                # Check if we have enough balance for the order
                if order_value > usdt_balance * 0.95:  # Leave 5% buffer
                    self.error_occurred.emit("Insufficient balance for order")
                    return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Risk check error: {str(e)}")
            return False

    # Simulation methods for demo mode
    def simulate_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict:
        """Simulate a limit order in demo mode"""
        order_id = f"demo_limit_{int(time.time() * 1000)}"
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'limit',
            'side': side,
            'amount': amount,
            'price': price,
            'status': 'open',
            'filled': 0,
            'remaining': amount,
            'timestamp': int(time.time() * 1000),
            'params': params
        }

        self.open_orders[order_id] = order
        return order

    def simulate_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict:
        """Simulate a market order in demo mode"""
        # Get current price
        ticker = self.get_ticker(symbol)
        fill_price = ticker['last'] if ticker else 0.35

        order_id = f"demo_market_{int(time.time() * 1000)}"
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'market',
            'side': side,
            'amount': amount,
            'price': fill_price,
            'status': 'closed',
            'filled': amount,
            'remaining': 0,
            'timestamp': int(time.time() * 1000),
            'params': params
        }

        # Update demo position
        self.update_demo_position(symbol, side, amount, fill_price)

        return order

    def update_demo_position(self, symbol: str, side: str, amount: float, price: float):
        """Update demo position after trade"""
        try:
            if symbol not in self.open_positions:
                self.open_positions[symbol] = {
                    'symbol': symbol,
                    'size': 0,
                    'side': None,
                    'unrealizedPnl': 0,
                    'percentage': 0,
                    'entryPrice': 0,
                    'markPrice': price
                }

            position = self.open_positions[symbol]

            if side == 'buy':
                if position['size'] <= 0:  # Opening long or reducing short
                    if position['size'] < 0:  # Reducing short
                        reduction = min(amount, abs(position['size']))
                        position['size'] += reduction
                        amount -= reduction

                    if amount > 0:  # Opening long
                        position['size'] += amount
                        position['side'] = 'long'
                        position['entryPrice'] = price

            else:  # sell
                if position['size'] >= 0:  # Opening short or reducing long
                    if position['size'] > 0:  # Reducing long
                        reduction = min(amount, position['size'])
                        position['size'] -= reduction
                        amount -= reduction

                    if amount > 0:  # Opening short
                        position['size'] -= amount
                        position['side'] = 'short'
                        position['entryPrice'] = price

            # Update mark price
            position['markPrice'] = price

            # Calculate unrealized PnL
            if position['size'] != 0:
                if position['side'] == 'long':
                    position['unrealizedPnl'] = position['size'] * (price - position['entryPrice'])
                else:
                    position['unrealizedPnl'] = abs(position['size']) * (position['entryPrice'] - price)
            else:
                position['unrealizedPnl'] = 0
                position['side'] = None

            self.position_updated.emit(position)

        except Exception as e:
            self.error_occurred.emit(f"Error updating demo position: {str(e)}")

    # Update methods
    def update_positions(self):
        """Update all positions (called by timer)"""
        try:
            if not self.is_connected:
                return

            positions = self.get_all_positions()
            for symbol, position in positions.items():
                self.position_updated.emit(position)

        except Exception as e:
            # Don't emit error for routine updates to avoid spam
            pass

    def update_balance(self):
        """Update account balance (called by timer)"""
        try:
            if not self.is_connected:
                return

            balance = self.get_balance()
            if balance:
                self.account_balance = balance
                self.balance_updated.emit(balance)

        except Exception as e:
            # Don't emit error for routine updates to avoid spam
            pass

    def reset_daily_pnl(self):
        """Reset daily PnL if new day"""
        try:
            now = datetime.now()
            if now.date() > self.daily_reset_time.date():
                self.daily_pnl = 0.0
                self.daily_reset_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                self.status_updated.emit("Daily PnL reset")
        except Exception as e:
            self.error_occurred.emit(f"Error resetting daily PnL: {str(e)}")

    # Utility methods
    def is_demo_mode(self) -> bool:
        """Check if running in demo mode"""
        return self.demo_mode

    def get_connection_status(self) -> bool:
        """Get connection status"""
        return self.is_connected

    def get_exchange_info(self) -> Dict:
        """Get exchange information"""
        return {
            'name': self.exchange_name,
            'demo_mode': self.demo_mode,
            'connected': self.is_connected,
            'open_orders': len(self.open_orders),
            'open_positions': len([p for p in self.open_positions.values() if p['size'] != 0])
        }
