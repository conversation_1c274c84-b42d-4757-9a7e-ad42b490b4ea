"""
Production Configuration Loader for Epinnox v6
Loads production settings and ensures live trading mode
"""

import yaml
from pathlib import Path

class ProductionConfig:
    """Production configuration manager"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent
        self.production_config_path = self.config_dir / "production_config.yaml"
        self.strategy_config_path = self.config_dir / "strategy.yaml"
        self.credentials_path = self.config_dir / "credentials.yaml"

        # Load configurations with production taking precedence
        self.strategy_config = self._load_strategy_config()
        self.production_config = self._load_production_config()
        self.credentials = self._load_credentials()

        # Merge configurations with production overriding strategy
        self.merged_config = self._merge_configurations()

    def _load_strategy_config(self):
        """Load strategy configuration"""
        try:
            with open(self.strategy_config_path, 'r') as f:
                config = yaml.safe_load(f)
            print("✓ Strategy configuration loaded")
            return config
        except Exception as e:
            print(f"⚠️ Error loading strategy config: {e}")
            return {}

    def _merge_configurations(self):
        """Merge strategy and production configs with production taking precedence"""
        merged = self.strategy_config.copy()

        # Override with production settings
        if 'development' in self.production_config:
            if 'development' not in merged:
                merged['development'] = {}
            merged['development'].update(self.production_config['development'])

        # Override other sections as needed
        for section in ['trading', 'risk', 'system', 'data']:
            if section in self.production_config:
                merged[section] = self.production_config[section]

        print("✓ Configuration merged with production precedence")
        return merged

    def _load_production_config(self):
        """Load production configuration"""
        try:
            with open(self.production_config_path, 'r') as f:
                config = yaml.safe_load(f)
            print("✓ Production configuration loaded")
            return config
        except Exception as e:
            print(f"⚠️ Error loading production config: {e}")
            return {}
    
    def _load_credentials(self):
        """Load API credentials"""
        try:
            with open(self.credentials_path, 'r') as f:
                creds = yaml.safe_load(f)
            print("✓ API credentials loaded")
            return creds
        except Exception as e:
            print(f"⚠️ Error loading credentials: {e}")
            return {}
    
    def is_live_trading_enabled(self):
        """Check if live trading is enabled (production config takes precedence)"""
        return not self.merged_config.get('development', {}).get('simulate_trades', True)
    
    def get_exchange_credentials(self):
        """Get exchange API credentials"""
        return {
            'exchange': self.credentials.get('exchange', 'htx'),
            'apiKey': self.credentials.get('apiKey', ''),
            'secret': self.credentials.get('secret', '')
        }
    
    def get_trading_config(self):
        """Get trading configuration (merged with production precedence)"""
        return self.merged_config.get('trading', {})

    def get_risk_config(self):
        """Get risk management configuration (merged with production precedence)"""
        return self.merged_config.get('risk', {})

    def get_system_config(self):
        """Get system configuration (merged with production precedence)"""
        return self.merged_config.get('system', {})
    
    def validate_production_readiness(self):
        """Validate that system is ready for production"""
        issues = []
        
        # Check credentials
        creds = self.get_exchange_credentials()
        if not creds['apiKey'] or not creds['secret']:
            issues.append("❌ Missing API credentials")
        else:
            print("✓ API credentials present")
        
        # Check live trading mode
        if not self.is_live_trading_enabled():
            issues.append("❌ Live trading not enabled (simulate_trades: true)")
        else:
            print("✓ Live trading enabled")
        
        # Check debug mode
        if self.merged_config.get('development', {}).get('debug_mode', False):
            issues.append("⚠️ Debug mode is enabled")

        # Check mock LLM
        if self.merged_config.get('development', {}).get('mock_llm', False):
            issues.append("⚠️ Mock LLM is enabled")
        
        return issues
    
    def print_production_status(self):
        """Print production readiness status"""
        print("\n" + "="*60)
        print("🚀 EPINNOX v6 PRODUCTION READINESS CHECK")
        print("="*60)
        
        issues = self.validate_production_readiness()
        
        if not issues:
            print("✅ SYSTEM READY FOR LIVE TRADING")
            print("🔥 All production requirements met")
            print("💰 Real trades will be executed")
            print("📊 Live market data active")
            print("🤖 Real LLM analysis enabled")
        else:
            print("⚠️ PRODUCTION ISSUES FOUND:")
            for issue in issues:
                print(f"   {issue}")
        
        # Print configuration summary
        print("\n📋 CONFIGURATION SUMMARY:")
        print(f"   Exchange: {self.get_exchange_credentials()['exchange'].upper()}")
        print(f"   Live Trading: {'✅ ENABLED' if self.is_live_trading_enabled() else '❌ DISABLED'}")
        print(f"   Debug Mode: {'❌ ON' if self.merged_config.get('development', {}).get('debug_mode', False) else '✅ OFF'}")
        print(f"   Mock LLM: {'❌ ON' if self.merged_config.get('development', {}).get('mock_llm', False) else '✅ OFF'}")
        
        print("="*60)
        return len(issues) == 0

# Global production config instance
production_config = ProductionConfig()

def get_production_config():
    """Get the global production configuration"""
    return production_config

def is_production_ready():
    """Check if system is ready for production"""
    return len(production_config.validate_production_readiness()) == 0

def get_live_trading_credentials():
    """Get credentials for live trading"""
    return production_config.get_exchange_credentials()

def get_demo_mode_setting():
    """Get demo mode setting from production configuration"""
    # In production, demo mode should be False (live trading enabled)
    return not production_config.is_live_trading_enabled()

def enforce_production_settings():
    """Enforce production settings across the system"""
    settings = {
        'demo_mode': get_demo_mode_setting(),
        'live_trading': production_config.is_live_trading_enabled(),
        'debug_mode': production_config.merged_config.get('development', {}).get('debug_mode', False),
        'mock_llm': production_config.merged_config.get('development', {}).get('mock_llm', False)
    }

    print("🔧 Enforcing production settings:")
    print(f"   Demo Mode: {'ON' if settings['demo_mode'] else 'OFF'}")
    print(f"   Live Trading: {'ON' if settings['live_trading'] else 'OFF'}")
    print(f"   Debug Mode: {'ON' if settings['debug_mode'] else 'OFF'}")
    print(f"   Mock LLM: {'ON' if settings['mock_llm'] else 'OFF'}")

    return settings
