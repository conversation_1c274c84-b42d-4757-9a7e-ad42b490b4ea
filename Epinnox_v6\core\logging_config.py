"""
Centralized Logging Configuration for Epinnox v6
Provides standardized logging patterns across all modules
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

class EpinnoxFormatter(logging.Formatter):
    """Custom formatter for Epinnox logging with color support"""
    
    # Color codes for different log levels
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def __init__(self, use_colors: bool = True):
        super().__init__()
        self.use_colors = use_colors and sys.stdout.isatty()
        
        # Format string with timestamp, level, module, and message
        self.format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
    def format(self, record):
        # Create a copy of the record to avoid modifying the original
        record_copy = logging.makeLogRecord(record.__dict__)
        
        # Add color if enabled
        if self.use_colors:
            level_color = self.COLORS.get(record_copy.levelname, '')
            reset_color = self.COLORS['RESET']
            record_copy.levelname = f"{level_color}{record_copy.levelname}{reset_color}"
        
        # Format the message
        formatter = logging.Formatter(self.format_string)
        return formatter.format(record_copy)

class EpinnoxLogger:
    """Centralized logger configuration for Epinnox"""
    
    def __init__(self, log_dir: str = "logs", max_file_size: int = 10*1024*1024, backup_count: int = 5):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.max_file_size = max_file_size  # 10MB default
        self.backup_count = backup_count
        
        # Configure root logger
        self._setup_root_logger()
        
        # Module-specific loggers
        self._module_loggers = {}
        
    def _setup_root_logger(self):
        """Setup the root logger with file and console handlers"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with colors
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(EpinnoxFormatter(use_colors=True))
        root_logger.addHandler(console_handler)
        
        # File handler for general logs
        log_file = self.log_dir / f"epinnox_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(EpinnoxFormatter(use_colors=False))
        root_logger.addHandler(file_handler)
        
        # Error file handler for errors only
        error_file = self.log_dir / f"epinnox_errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(EpinnoxFormatter(use_colors=False))
        root_logger.addHandler(error_handler)
        
    def get_logger(self, name: str, level: Optional[str] = None) -> logging.Logger:
        """Get a logger for a specific module"""
        if name in self._module_loggers:
            return self._module_loggers[name]
        
        logger = logging.getLogger(name)
        
        # Set level if specified
        if level:
            numeric_level = getattr(logging, level.upper(), logging.INFO)
            logger.setLevel(numeric_level)
        
        # Add module-specific file handler if needed
        if name in ['trading', 'ml', 'llm', 'data', 'performance']:
            self._add_module_handler(logger, name)
        
        self._module_loggers[name] = logger
        return logger
    
    def _add_module_handler(self, logger: logging.Logger, module_name: str):
        """Add module-specific file handler"""
        log_file = self.log_dir / f"{module_name}_{datetime.now().strftime('%Y%m%d')}.log"
        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        handler.setLevel(logging.DEBUG)
        handler.setFormatter(EpinnoxFormatter(use_colors=False))
        logger.addHandler(handler)
    
    def set_debug_mode(self, enabled: bool = True):
        """Enable or disable debug mode"""
        level = logging.DEBUG if enabled else logging.INFO
        logging.getLogger().setLevel(level)
        
        # Update all module loggers
        for logger in self._module_loggers.values():
            logger.setLevel(level)
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """Clean up old log files"""
        try:
            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
            
            for log_file in self.log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    print(f"Removed old log file: {log_file}")
                    
        except Exception as e:
            print(f"Error cleaning up logs: {e}")

# Global logger instance
_epinnox_logger = None

def initialize_logging(log_dir: str = "logs", debug_mode: bool = False, **kwargs) -> EpinnoxLogger:
    """Initialize the global logging system"""
    global _epinnox_logger
    
    _epinnox_logger = EpinnoxLogger(log_dir=log_dir, **kwargs)
    
    if debug_mode:
        _epinnox_logger.set_debug_mode(True)
    
    # Clean up old logs
    _epinnox_logger.cleanup_old_logs()
    
    return _epinnox_logger

def get_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """Get a logger for a module"""
    global _epinnox_logger
    
    if _epinnox_logger is None:
        _epinnox_logger = initialize_logging()
    
    return _epinnox_logger.get_logger(name, level)

def set_debug_mode(enabled: bool = True):
    """Enable or disable debug mode globally"""
    global _epinnox_logger
    
    if _epinnox_logger is not None:
        _epinnox_logger.set_debug_mode(enabled)

# Convenience functions for common logging patterns
def log_operation_start(logger: logging.Logger, operation: str, **kwargs):
    """Log the start of an operation"""
    params = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logger.info(f"Starting {operation}" + (f" with {params}" if params else ""))

def log_operation_success(logger: logging.Logger, operation: str, duration: float = None, **kwargs):
    """Log successful completion of an operation"""
    duration_str = f" in {duration:.3f}s" if duration is not None else ""
    result_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logger.info(f"Completed {operation}{duration_str}" + (f" - {result_str}" if result_str else ""))

def log_operation_error(logger: logging.Logger, operation: str, error: Exception, **kwargs):
    """Log an operation error"""
    context = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logger.error(f"Failed {operation}: {str(error)}" + (f" - {context}" if context else ""))

def log_performance_metric(logger: logging.Logger, metric_name: str, value: float, unit: str = ""):
    """Log a performance metric"""
    unit_str = f" {unit}" if unit else ""
    logger.info(f"Performance: {metric_name} = {value:.3f}{unit_str}")

def log_trading_action(logger: logging.Logger, action: str, symbol: str, **kwargs):
    """Log a trading action"""
    details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logger.info(f"Trading: {action} {symbol}" + (f" - {details}" if details else ""))

# Decorator for automatic operation logging
def log_operation(operation_name: str, log_args: bool = False, log_result: bool = False):
    """Decorator to automatically log operation start, success, and errors"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Get logger from the first argument if it's a class instance
            logger = None
            if args and hasattr(args[0], '__class__'):
                module_name = args[0].__class__.__module__
                logger = get_logger(module_name)
            else:
                logger = get_logger(func.__module__)
            
            # Log operation start
            log_kwargs = {}
            if log_args:
                log_kwargs.update({f"arg_{i}": str(arg) for i, arg in enumerate(args[1:])})
                log_kwargs.update({k: str(v) for k, v in kwargs.items()})
            
            log_operation_start(logger, operation_name, **log_kwargs)
            
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                result_kwargs = {}
                if log_result and result is not None:
                    result_kwargs['result'] = str(result)[:100]  # Limit result length
                
                log_operation_success(logger, operation_name, duration, **result_kwargs)
                return result
                
            except Exception as e:
                log_operation_error(logger, operation_name, e)
                raise
        
        return wrapper
    return decorator
