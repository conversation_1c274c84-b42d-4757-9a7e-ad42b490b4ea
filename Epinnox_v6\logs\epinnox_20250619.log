2025-06-19 06:55:44,406 - main - INFO - Epinnox v6 starting up...
2025-06-19 06:55:44,438 - core.performance_monitor - INFO - Performance monitoring started
2025-06-19 06:55:44,439 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-19 06:55:44,441 - main - INFO - Performance monitoring initialized
2025-06-19 06:55:44,451 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-19 06:55:44,452 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-19 06:55:44,453 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-19 06:55:50,320 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-19 06:55:51,388 - websocket - INFO - Websocket connected
2025-06-19 06:55:54,242 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-19 06:55:54,243 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-19 06:55:54,243 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-19 06:55:54,243 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-19 06:55:54,248 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-19 06:55:56,315 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-19 06:55:56,316 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-19 06:55:56,316 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
[06:55:56] ✓ LMStudio runner initialized with 8 models
2025-06-19 06:55:56,324 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-19 06:55:56,324 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-19 06:55:56,336 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-19 06:55:56,338 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-19 06:55:56,339 - storage.session_manager - INFO - Session Manager initialized
2025-06-19 06:55:56,346 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250619_065556_df7d1776
2025-06-19 06:55:56,350 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250619_065556_df7d1776
[06:55:56] 📊 Session started: live_DOGEUSDTUSDT_20250619_065556_df7d1776
[06:55:57] Connected to live data
