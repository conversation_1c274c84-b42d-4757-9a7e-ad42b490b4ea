"""
WebSocket Client for Live Market Data
Handles real-time data feeds from exchanges using WebSocket connections
"""

import asyncio
import json
import websocket
import threading
import time
from datetime import datetime
from typing import Dict, List, Callable, Optional
from PyQt5.QtCore import QObject, pyqtSignal


class WebSocketClient(QObject):
    """
    WebSocket client for real-time market data from exchanges
    Supports HTX/Huobi and other CCXT-compatible exchanges
    """
    
    # PyQt signals for thread-safe UI updates
    price_update = pyqtSignal(str, dict)  # symbol, price_data
    orderbook_update = pyqtSignal(str, dict)  # symbol, orderbook_data
    trade_update = pyqtSignal(str, dict)  # symbol, trade_data
    connection_status = pyqtSignal(bool)  # connected/disconnected
    error_occurred = pyqtSignal(str)  # error message
    
    def __init__(self, exchange_name="htx"):
        super().__init__()
        self.exchange_name = exchange_name
        self.ws = None
        self.is_connected = False
        self.subscribed_symbols = set()
        self.callbacks = {}
        self.thread = None
        
        # WebSocket URLs for different exchanges
        self.ws_urls = {
            "htx": "wss://api.huobi.pro/ws",
            "binance": "wss://stream.binance.com:9443/ws/",
            "okx": "wss://ws.okx.com:8443/ws/v5/public"
        }
        
        # Latest data cache
        self.latest_prices = {}
        self.latest_orderbooks = {}
        self.latest_trades = {}
        
    def connect(self):
        """Connect to WebSocket"""
        try:
            if self.is_connected:
                print(f"✓ WebSocket already connected to {self.exchange_name}")
                return True

            # Check if thread is already running
            if self.thread and self.thread.is_alive():
                print(f"✓ WebSocket thread already running for {self.exchange_name}")
                return True

            url = self.ws_urls.get(self.exchange_name)
            if not url:
                self.error_occurred.emit(f"Unsupported exchange: {self.exchange_name}")
                return False

            print(f"🔗 Starting WebSocket connection to {self.exchange_name}")

            # Start WebSocket in separate thread
            self.thread = threading.Thread(target=self._run_websocket, args=(url,))
            self.thread.daemon = True
            self.thread.start()

            return True

        except Exception as e:
            self.error_occurred.emit(f"WebSocket connection error: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from WebSocket"""
        try:
            self.is_connected = False
            if self.ws:
                self.ws.close()
            self.connection_status.emit(False)
        except Exception as e:
            self.error_occurred.emit(f"WebSocket disconnect error: {str(e)}")
    
    def subscribe_ticker(self, symbol: str):
        """Subscribe to ticker/price updates for a symbol"""
        try:
            if not self.is_connected:
                self.connect()

            # Check if already subscribed
            subscription_key = f"ticker_{symbol}"
            if hasattr(self, '_active_subscriptions'):
                if subscription_key in self._active_subscriptions:
                    return
            else:
                self._active_subscriptions = set()

            self.subscribed_symbols.add(symbol)
            self._active_subscriptions.add(subscription_key)

            if self.exchange_name == "htx":
                # Convert symbol format for HTX (DOGE/USDT:USDT -> dogeusdt)
                htx_symbol = self._convert_symbol_to_htx(symbol)
                # HTX ticker subscription format
                sub_msg = {
                    "sub": f"market.{htx_symbol}.ticker",
                    "id": subscription_key
                }
                self._send_message(sub_msg)

        except Exception as e:
            self.error_occurred.emit(f"Ticker subscription error: {str(e)}")
    
    def subscribe_orderbook(self, symbol: str):
        """Subscribe to order book updates for a symbol"""
        try:
            if not self.is_connected:
                self.connect()

            # Check if already subscribed
            subscription_key = f"depth_{symbol}"
            if hasattr(self, '_active_subscriptions'):
                if subscription_key in self._active_subscriptions:
                    return
            else:
                self._active_subscriptions = set()

            self._active_subscriptions.add(subscription_key)

            if self.exchange_name == "htx":
                # Convert symbol format for HTX
                htx_symbol = self._convert_symbol_to_htx(symbol)
                # HTX order book subscription format
                sub_msg = {
                    "sub": f"market.{htx_symbol}.depth.step0",
                    "id": subscription_key
                }
                self._send_message(sub_msg)

        except Exception as e:
            self.error_occurred.emit(f"Order book subscription error: {str(e)}")
    
    def subscribe_trades(self, symbol: str):
        """Subscribe to trade updates for a symbol"""
        try:
            if not self.is_connected:
                self.connect()

            # Check if already subscribed
            subscription_key = f"trade_{symbol}"
            if hasattr(self, '_active_subscriptions'):
                if subscription_key in self._active_subscriptions:
                    return
            else:
                self._active_subscriptions = set()

            self._active_subscriptions.add(subscription_key)

            if self.exchange_name == "htx":
                # Convert symbol format for HTX
                htx_symbol = self._convert_symbol_to_htx(symbol)
                # HTX trades subscription format
                sub_msg = {
                    "sub": f"market.{htx_symbol}.trade.detail",
                    "id": subscription_key
                }
                self._send_message(sub_msg)

        except Exception as e:
            self.error_occurred.emit(f"Trades subscription error: {str(e)}")
    
    def _run_websocket(self, url: str):
        """Run WebSocket connection in separate thread"""
        try:
            websocket.enableTrace(False)  # Disable debug traces
            self.ws = websocket.WebSocketApp(
                url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            self.ws.run_forever()
        except Exception as e:
            self.error_occurred.emit(f"WebSocket thread error: {str(e)}")
    
    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.is_connected = True
        self.connection_status.emit(True)
        print(f"WebSocket connected to {self.exchange_name}")

        # Clear active subscriptions to allow re-subscription after reconnection
        if hasattr(self, '_active_subscriptions'):
            self._active_subscriptions.clear()

        # Re-subscribe to all symbols after connection
        for symbol in self.subscribed_symbols:
            self.subscribe_ticker(symbol)
            self.subscribe_orderbook(symbol)
            self.subscribe_trades(symbol)
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket connection closed"""
        self.is_connected = False
        self.connection_status.emit(False)
        print(f"WebSocket disconnected from {self.exchange_name}")
    
    def _on_error(self, ws, error):
        """WebSocket error occurred"""
        self.error_occurred.emit(f"WebSocket error: {str(error)}")
        print(f"WebSocket error: {error}")
    
    def _on_message(self, ws, message):
        """Process incoming WebSocket message"""
        try:
            # Handle HTX compressed messages
            if self.exchange_name == "htx":
                import gzip
                try:
                    # Try to decompress if it's gzipped
                    data = gzip.decompress(message).decode('utf-8')
                except:
                    # If not compressed, use as is
                    data = message
            else:
                data = message
            
            # Parse JSON
            msg = json.loads(data)
            
            # Handle ping/pong for HTX
            if "ping" in msg:
                pong_msg = {"pong": msg["ping"]}
                self._send_message(pong_msg)
                # Ping/pong is working - no need to log every heartbeat
                return
            
            # Process data updates
            self._process_message(msg)
            
        except Exception as e:
            self.error_occurred.emit(f"Message processing error: {str(e)}")
    
    def _process_message(self, msg: dict):
        """Process parsed WebSocket message"""
        try:
            if self.exchange_name == "htx":
                self._process_htx_message(msg)
        except Exception as e:
            self.error_occurred.emit(f"Message processing error: {str(e)}")
    
    def _process_htx_message(self, msg: dict):
        """Process HTX-specific message format"""
        try:
            # Handle subscription confirmations
            if "subbed" in msg:
                print(f"HTX subscription confirmed: {msg['subbed']}")
                return

            # Handle subscription errors
            if "err-code" in msg:
                print(f"HTX subscription error: {msg}")
                return

            ch = msg.get("ch", "")
            tick = msg.get("tick", {})

            if not ch or not tick:
                return
            
            # Extract symbol from channel
            symbol_parts = ch.split(".")
            if len(symbol_parts) < 2:
                return

            raw_symbol = symbol_parts[1].upper()
            # Convert back to standard format (e.g., dogeusdt -> DOGE/USDT:USDT)
            symbol = self._convert_symbol_from_htx(raw_symbol)
            
            # Process different data types
            if "ticker" in ch:
                price_data = {
                    "symbol": symbol,
                    "price": tick.get("close", 0),
                    "high": tick.get("high", 0),
                    "low": tick.get("low", 0),
                    "volume": tick.get("vol", 0),
                    "timestamp": datetime.now().timestamp()
                }
                self.latest_prices[symbol] = price_data
                self.price_update.emit(symbol, price_data)
                
            elif "depth" in ch:
                orderbook_data = {
                    "symbol": symbol,
                    "bids": tick.get("bids", []),
                    "asks": tick.get("asks", []),
                    "timestamp": datetime.now().timestamp()
                }
                self.latest_orderbooks[symbol] = orderbook_data
                self.orderbook_update.emit(symbol, orderbook_data)
                
            elif "trade" in ch:
                trades = tick.get("data", [])
                if trades:
                    trade_data = {
                        "symbol": symbol,
                        "trades": trades,
                        "timestamp": datetime.now().timestamp()
                    }
                    self.latest_trades[symbol] = trade_data
                    self.trade_update.emit(symbol, trade_data)
                    
        except Exception as e:
            self.error_occurred.emit(f"HTX message processing error: {str(e)}")
    
    def _send_message(self, msg: dict):
        """Send message to WebSocket"""
        try:
            if self.ws and self.is_connected:
                self.ws.send(json.dumps(msg))
        except Exception as e:
            self.error_occurred.emit(f"Send message error: {str(e)}")
    
    def get_latest_price(self, symbol: str) -> Optional[dict]:
        """Get latest price data for symbol"""
        return self.latest_prices.get(symbol)
    
    def get_latest_orderbook(self, symbol: str) -> Optional[dict]:
        """Get latest order book data for symbol"""
        return self.latest_orderbooks.get(symbol)
    
    def get_latest_trades(self, symbol: str) -> Optional[dict]:
        """Get latest trades data for symbol"""
        return self.latest_trades.get(symbol)

    def _convert_symbol_to_htx(self, symbol: str) -> str:
        """Convert standard symbol format to HTX format"""
        try:
            # Convert DOGE/USDT:USDT -> dogeusdt
            if "/" in symbol and ":" in symbol:
                # Futures format: DOGE/USDT:USDT
                base_quote = symbol.split(":")[0]  # DOGE/USDT
                base, quote = base_quote.split("/")  # DOGE, USDT
                return f"{base.lower()}{quote.lower()}"
            elif "/" in symbol:
                # Spot format: DOGE/USDT
                base, quote = symbol.split("/")
                return f"{base.lower()}{quote.lower()}"
            else:
                # Already in correct format
                return symbol.lower()
        except Exception as e:
            print(f"Error converting symbol {symbol}: {e}")
            return symbol.lower()

    def _convert_symbol_from_htx(self, htx_symbol: str) -> str:
        """Convert HTX symbol format back to standard format"""
        try:
            # Convert dogeusdt -> DOGE/USDT:USDT
            htx_symbol = htx_symbol.upper()
            if htx_symbol.endswith("USDT"):
                base = htx_symbol[:-4]
                return f"{base}/USDT:USDT"
            else:
                # For other pairs, try to guess the format
                return htx_symbol
        except Exception as e:
            print(f"Error converting HTX symbol {htx_symbol}: {e}")
            return htx_symbol
