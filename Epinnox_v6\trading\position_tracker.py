"""
Position Tracker for Epinnox v6
Real-time position monitoring and PnL calculation
"""

import time
from datetime import datetime
from typing import Dict, List, Optional
from PyQt5.QtCore import QObject, QTimer, pyqtSignal
from dataclasses import dataclass, asdict


@dataclass
class Position:
    """Position data structure"""
    symbol: str
    side: str  # 'long', 'short', or None
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    realized_pnl: float
    percentage: float
    leverage: int
    margin: float
    timestamp: float
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return asdict(self)


@dataclass
class Trade:
    """Trade data structure"""
    id: str
    symbol: str
    side: str
    type: str  # 'market', 'limit'
    amount: float
    price: float
    fee: float
    timestamp: float
    pnl: float = 0.0
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return asdict(self)


class PositionTracker(QObject):
    """
    Tracks positions, calculates PnL, and manages trade history
    """
    
    # Signals
    position_updated = pyqtSignal(dict)  # position data
    trade_executed = pyqtSignal(dict)    # trade data
    pnl_updated = pyqtSignal(float, float)  # unrealized_pnl, realized_pnl
    margin_warning = pyqtSignal(str, float)  # symbol, margin_ratio
    
    def __init__(self):
        super().__init__()
        
        # Position storage
        self.positions: Dict[str, Position] = {}
        self.trade_history: List[Trade] = []
        
        # PnL tracking
        self.total_unrealized_pnl = 0.0
        self.total_realized_pnl = 0.0
        self.daily_pnl = 0.0
        self.session_pnl = 0.0
        
        # Risk monitoring
        self.margin_warning_threshold = 0.8  # 80% margin usage warning
        self.liquidation_threshold = 0.95    # 95% margin usage critical
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_all_positions)
        self.update_timer.start(1000)  # Update every second
    
    def add_trade(self, trade_data: Dict):
        """Add a new trade and update positions"""
        try:
            trade = Trade(
                id=trade_data.get('id', ''),
                symbol=trade_data.get('symbol', ''),
                side=trade_data.get('side', ''),
                type=trade_data.get('type', ''),
                amount=trade_data.get('amount', 0.0),
                price=trade_data.get('price', 0.0),
                fee=trade_data.get('fee', 0.0),
                timestamp=trade_data.get('timestamp', time.time() * 1000)
            )
            
            # Add to trade history
            self.trade_history.append(trade)
            
            # Update position
            self.update_position_from_trade(trade)
            
            # Emit signals
            self.trade_executed.emit(trade.to_dict())
            
        except Exception as e:
            print(f"Error adding trade: {e}")
    
    def update_position_from_trade(self, trade: Trade):
        """Update position based on trade execution"""
        try:
            symbol = trade.symbol
            
            # Get or create position
            if symbol not in self.positions:
                self.positions[symbol] = Position(
                    symbol=symbol,
                    side=None,
                    size=0.0,
                    entry_price=0.0,
                    mark_price=trade.price,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    percentage=0.0,
                    leverage=1,
                    margin=0.0,
                    timestamp=trade.timestamp
                )
            
            position = self.positions[symbol]
            
            # Calculate position changes
            if trade.side == 'buy':
                if position.size <= 0:  # Opening long or reducing short
                    if position.size < 0:  # Reducing short position
                        reduction = min(trade.amount, abs(position.size))
                        # Calculate realized PnL for short reduction
                        pnl = reduction * (position.entry_price - trade.price)
                        position.realized_pnl += pnl
                        self.total_realized_pnl += pnl
                        trade.pnl = pnl
                        
                        position.size += reduction
                        trade.amount -= reduction
                    
                    if trade.amount > 0:  # Opening new long position
                        if position.size == 0:
                            position.entry_price = trade.price
                            position.side = 'long'
                        else:
                            # Average entry price
                            total_cost = (position.size * position.entry_price) + (trade.amount * trade.price)
                            position.size += trade.amount
                            position.entry_price = total_cost / position.size
                        
                        position.size += trade.amount
                        
                else:  # Adding to long position
                    total_cost = (position.size * position.entry_price) + (trade.amount * trade.price)
                    position.size += trade.amount
                    position.entry_price = total_cost / position.size
            
            else:  # sell
                if position.size >= 0:  # Opening short or reducing long
                    if position.size > 0:  # Reducing long position
                        reduction = min(trade.amount, position.size)
                        # Calculate realized PnL for long reduction
                        pnl = reduction * (trade.price - position.entry_price)
                        position.realized_pnl += pnl
                        self.total_realized_pnl += pnl
                        trade.pnl = pnl
                        
                        position.size -= reduction
                        trade.amount -= reduction
                    
                    if trade.amount > 0:  # Opening new short position
                        if position.size == 0:
                            position.entry_price = trade.price
                            position.side = 'short'
                        else:
                            # Average entry price
                            total_cost = (abs(position.size) * position.entry_price) + (trade.amount * trade.price)
                            position.size -= trade.amount
                            position.entry_price = total_cost / abs(position.size)
                        
                        position.size -= trade.amount
                        
                else:  # Adding to short position
                    total_cost = (abs(position.size) * position.entry_price) + (trade.amount * trade.price)
                    position.size -= trade.amount
                    position.entry_price = total_cost / abs(position.size)
            
            # Update position metadata
            position.mark_price = trade.price
            position.timestamp = trade.timestamp
            
            # Determine position side
            if position.size > 0:
                position.side = 'long'
            elif position.size < 0:
                position.side = 'short'
            else:
                position.side = None
            
            # Calculate unrealized PnL
            self.calculate_unrealized_pnl(position)
            
            # Emit position update
            self.position_updated.emit(position.to_dict())
            
        except Exception as e:
            print(f"Error updating position from trade: {e}")
    
    def update_mark_price(self, symbol: str, mark_price: float):
        """Update mark price for a position with throttling to prevent spam"""
        try:
            if symbol in self.positions:
                position = self.positions[symbol]
                old_mark_price = position.mark_price
                position.mark_price = mark_price
                position.timestamp = time.time() * 1000

                # Recalculate unrealized PnL
                self.calculate_unrealized_pnl(position)

                # Throttle position updates - only emit if significant change or time passed
                current_time = time.time()
                if not hasattr(self, '_last_position_update_time'):
                    self._last_position_update_time = {}

                last_update_time = self._last_position_update_time.get(symbol, 0)
                price_change = abs(mark_price - old_mark_price) if old_mark_price else 0

                # Emit update if: significant price change (>0.1%) OR 10 seconds passed
                significant_change = price_change > (old_mark_price * 0.001) if old_mark_price else True
                time_passed = current_time - last_update_time > 10

                if significant_change or time_passed:
                    self.position_updated.emit(position.to_dict())
                    self._last_position_update_time[symbol] = current_time

        except Exception as e:
            print(f"Error updating mark price: {e}")
    
    def calculate_unrealized_pnl(self, position: Position):
        """Calculate unrealized PnL for a position with proper HTX contract calculation"""
        try:
            if position.size == 0 or position.entry_price == 0 or position.mark_price == 0:
                position.unrealized_pnl = 0.0
                position.percentage = 0.0
                return

            # HTX Linear Swaps: Each contract represents $1 USD value
            # PnL = (Price Difference) × Size × Contract Value ($1)
            price_diff = 0.0

            if position.side == 'long':
                # Long: profit when price goes up
                price_diff = position.mark_price - position.entry_price
                position.unrealized_pnl = position.size * price_diff
                position.percentage = (price_diff / position.entry_price) * 100
            elif position.side == 'short':
                # Short: profit when price goes down
                price_diff = position.entry_price - position.mark_price
                position.unrealized_pnl = abs(position.size) * price_diff
                position.percentage = (price_diff / position.entry_price) * 100

            # Apply leverage to percentage (leverage amplifies returns)
            if position.leverage > 0:
                position.percentage *= position.leverage

            # Debug logging only for significant changes
            if hasattr(self, '_last_pnl_log') and hasattr(self, '_last_pnl_log_time'):
                import time
                current_time = time.time()
                last_pnl = self._last_pnl_log.get(position.symbol, 0)
                last_time = self._last_pnl_log_time.get(position.symbol, 0)

                # Only log if PnL changed significantly or 30 seconds passed
                if (abs(position.unrealized_pnl - last_pnl) > 0.10 or
                    current_time - last_time > 30):
                    print(f"📊 {position.symbol} PnL: ${position.unrealized_pnl:.4f} ({position.percentage:.2f}%) | {position.side.upper()} {abs(position.size)} @ {position.entry_price:.6f} → {position.mark_price:.6f}")
                    self._last_pnl_log[position.symbol] = position.unrealized_pnl
                    self._last_pnl_log_time[position.symbol] = current_time
            else:
                # Initialize tracking
                self._last_pnl_log = {position.symbol: position.unrealized_pnl}
                self._last_pnl_log_time = {position.symbol: time.time()}

        except Exception as e:
            print(f"Error calculating unrealized PnL: {e}")
            position.unrealized_pnl = 0.0
            position.percentage = 0.0
    
    def update_all_positions(self):
        """Update all positions and calculate totals"""
        try:
            total_unrealized = 0.0
            
            for position in self.positions.values():
                if position.size != 0:
                    total_unrealized += position.unrealized_pnl
                    
                    # Check margin warnings
                    self.check_margin_warning(position)
            
            self.total_unrealized_pnl = total_unrealized
            
            # Emit PnL update
            self.pnl_updated.emit(self.total_unrealized_pnl, self.total_realized_pnl)
            
        except Exception as e:
            print(f"Error updating all positions: {e}")
    
    def check_margin_warning(self, position: Position):
        """Check for margin warnings with spam prevention"""
        try:
            if position.margin <= 0:
                return

            # Calculate margin ratio properly for HTX
            # Margin Ratio = Used Margin / (Used Margin + Unrealized PnL)
            # For HTX: Used Margin = Position Value / Leverage
            position_value = abs(position.size) * position.entry_price
            used_margin = position_value / max(position.leverage, 1)

            # Account for unrealized PnL in available margin
            available_margin = used_margin + position.unrealized_pnl

            if available_margin > 0:
                margin_ratio = used_margin / available_margin
            else:
                margin_ratio = 1.0  # 100% if no available margin

            # Spam prevention - only emit warnings every 30 seconds
            import time
            current_time = time.time()

            if not hasattr(self, '_last_margin_warning_time'):
                self._last_margin_warning_time = {}

            last_warning_time = self._last_margin_warning_time.get(position.symbol, 0)

            # Only emit warning if 30 seconds have passed since last warning
            if current_time - last_warning_time > 30:
                if margin_ratio >= self.liquidation_threshold:
                    self.margin_warning.emit(f"LIQUIDATION RISK: {position.symbol}", margin_ratio)
                    self._last_margin_warning_time[position.symbol] = current_time
                elif margin_ratio >= self.margin_warning_threshold:
                    self.margin_warning.emit(f"MARGIN WARNING: {position.symbol}", margin_ratio)
                    self._last_margin_warning_time[position.symbol] = current_time

        except Exception as e:
            print(f"Error checking margin warning: {e}")
    
    def get_position(self, symbol: str) -> Optional[Dict]:
        """Get position for a symbol"""
        position = self.positions.get(symbol)
        return position.to_dict() if position else None
    
    def get_all_positions(self) -> Dict[str, Dict]:
        """Get all positions"""
        return {symbol: pos.to_dict() for symbol, pos in self.positions.items()}
    
    def get_open_positions(self) -> Dict[str, Dict]:
        """Get only open positions (size != 0)"""
        return {
            symbol: pos.to_dict() 
            for symbol, pos in self.positions.items() 
            if pos.size != 0
        }
    
    def get_trade_history(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """Get trade history"""
        trades = self.trade_history
        
        if symbol:
            trades = [t for t in trades if t.symbol == symbol]
        
        # Sort by timestamp (newest first) and limit
        trades = sorted(trades, key=lambda x: x.timestamp, reverse=True)
        return [t.to_dict() for t in trades[:limit]]
    
    def get_pnl_summary(self) -> Dict:
        """Get PnL summary"""
        return {
            'total_unrealized_pnl': self.total_unrealized_pnl,
            'total_realized_pnl': self.total_realized_pnl,
            'daily_pnl': self.daily_pnl,
            'session_pnl': self.session_pnl,
            'total_pnl': self.total_unrealized_pnl + self.total_realized_pnl
        }
    
    def reset_session_pnl(self):
        """Reset session PnL"""
        self.session_pnl = 0.0
    
    def reset_daily_pnl(self):
        """Reset daily PnL"""
        self.daily_pnl = 0.0
    
    def close_position(self, symbol: str):
        """Close a position (set size to 0)"""
        if symbol in self.positions:
            position = self.positions[symbol]
            
            # Add realized PnL to total
            self.total_realized_pnl += position.unrealized_pnl
            
            # Reset position
            position.size = 0.0
            position.side = None
            position.unrealized_pnl = 0.0
            position.percentage = 0.0
            
            # Emit update
            self.position_updated.emit(position.to_dict())
    
    def clear_all_positions(self):
        """Clear all positions"""
        self.positions.clear()
        self.total_unrealized_pnl = 0.0
        self.pnl_updated.emit(0.0, self.total_realized_pnl)
