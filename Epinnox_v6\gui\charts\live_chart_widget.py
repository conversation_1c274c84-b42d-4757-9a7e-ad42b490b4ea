"""
Live Chart Widget for Epinnox v6
Enhanced PyQtGraph chart with real-time data integration and professional features
"""

import pyqtgraph as pg
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel, QCheckBox
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QColor
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional

from ..matrix_theme import MatrixTheme
try:
    from .candlestick_chart import CandlestickChart
except ImportError:
    # Fallback if candlestick chart not available
    CandlestickChart = None

try:
    from .signal_overlay import SignalOverlay
    from ...ml.signal_generator import SignalManager, TradingSignal
    SIGNALS_AVAILABLE = True
except ImportError:
    print("⚠ Signal components not available - signals disabled")
    SIGNALS_AVAILABLE = False
    SignalOverlay = None
    SignalManager = None


class LiveChartWidget(QWidget):
    """
    Enhanced chart widget with live data integration
    Supports candlestick charts, real-time updates, and trading overlays
    """
    
    # Signals
    chart_clicked = pyqtSignal(float, float)  # price, timestamp
    symbol_changed = pyqtSignal(str)  # new symbol
    timeframe_changed = pyqtSignal(str)  # new timeframe
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Configuration
        self.current_symbol = "DOGE/USDT:USDT"
        self.current_timeframe = "1m"
        self.chart_type = "Tick"  # Default to Tick chart
        self.show_volume = True
        self.show_bid_ask = True
        
        # Data storage
        self.ohlcv_data = []
        self.volume_data = []
        self.bid_ask_data = {"bid": None, "ask": None}
        self.trade_data = []  # Store individual trades
        self.latest_price = None
        self.latest_volume = None
        
        # Chart items
        self.candlestick_item = None
        self.volume_bars = []
        self.bid_line = None
        self.ask_line = None
        self.price_line = None
        self.trade_scatter = None  # For individual trade points
        self.buy_trades = []  # Green dots for buys
        self.sell_trades = []  # Red dots for sells

        # Signal overlay and ML components
        self.signal_overlay = None
        self.signal_manager = None
        if SIGNALS_AVAILABLE:
            self.signal_manager = SignalManager()
            print("✅ ML/LLM Signal Manager initialized")
        
        # Initialize UI
        self.init_ui()
        
        # Setup update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(100)  # Update every 100ms for smooth animation
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # Chart controls
        controls_layout = self.create_controls()
        layout.addLayout(controls_layout)
        
        # Main chart area
        self.chart_widget = self.create_chart()
        layout.addWidget(self.chart_widget)
        
        # Volume chart (optional)
        self.volume_widget = self.create_volume_chart()
        layout.addWidget(self.volume_widget)
        
        # Chart info panel
        info_layout = self.create_info_panel()
        layout.addLayout(info_layout)
    
    def create_controls(self) -> QHBoxLayout:
        """Create chart control panel"""
        layout = QHBoxLayout()
        
        # Symbol selector
        layout.addWidget(QLabel("Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            "DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", 
            "SOL/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT"
        ])
        self.symbol_combo.setCurrentText(self.current_symbol)
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        layout.addWidget(self.symbol_combo)
        
        # Timeframe selector
        layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText(self.current_timeframe)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        layout.addWidget(self.timeframe_combo)
        
        # Chart type selector
        layout.addWidget(QLabel("Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Tick", "Line", "Candlestick"])
        self.chart_type_combo.setCurrentText(self.chart_type)
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        layout.addWidget(self.chart_type_combo)
        
        # Display options
        self.volume_checkbox = QCheckBox("Volume")
        self.volume_checkbox.setChecked(self.show_volume)
        self.volume_checkbox.stateChanged.connect(self.on_volume_toggle)
        layout.addWidget(self.volume_checkbox)
        
        self.bid_ask_checkbox = QCheckBox("Bid/Ask")
        self.bid_ask_checkbox.setChecked(self.show_bid_ask)
        self.bid_ask_checkbox.stateChanged.connect(self.on_bid_ask_toggle)
        layout.addWidget(self.bid_ask_checkbox)
        
        layout.addStretch()
        
        # Connection status
        self.status_label = QLabel("Disconnected")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        return layout
    
    def create_chart(self) -> pg.PlotWidget:
        """Create main price chart"""
        # Create chart with Matrix theme
        chart = pg.PlotWidget(
            background=MatrixTheme.BLACK,
            enableMenu=False
        )
        
        # Configure chart appearance
        chart.setLabel('left', 'Price', color=MatrixTheme.GREEN)
        chart.setLabel('bottom', 'Time', color=MatrixTheme.GREEN)
        chart.showGrid(x=True, y=True, alpha=0.3)
        
        # Set Matrix theme colors for axes
        chart.getAxis('left').setPen(MatrixTheme.GREEN)
        chart.getAxis('bottom').setPen(MatrixTheme.GREEN)
        chart.getAxis('left').setTextPen(MatrixTheme.GREEN)
        chart.getAxis('bottom').setTextPen(MatrixTheme.GREEN)

        # Set up time axis formatting to handle Unix timestamps
        bottom_axis = chart.getAxis('bottom')
        bottom_axis.enableAutoSIPrefix(False)  # Disable scientific notation

        # Use DateAxisItem for proper time formatting
        try:
            from pyqtgraph import DateAxisItem
            time_axis = DateAxisItem(orientation='bottom')
            time_axis.setPen(MatrixTheme.GREEN)
            time_axis.setTextPen(MatrixTheme.GREEN)
            chart.setAxisItems({'bottom': time_axis})
        except ImportError:
            # Fallback: just disable scientific notation
            bottom_axis.setTickSpacing(major=300, minor=60)  # Major ticks every 5 minutes
        
        # Connect click events
        chart.scene().sigMouseClicked.connect(self.on_chart_clicked)
        
        # Enable enhanced crosshair cursor for precision measurement
        self.crosshair = pg.CrosshairROI(
            pos=(0, 0),
            size=(1, 1),
            pen=pg.mkPen(MatrixTheme.YELLOW, width=1, style=pg.QtCore.Qt.DashLine)
        )
        chart.addItem(self.crosshair)

        # Add crosshair label for precise values
        self.crosshair_label = pg.TextItem(
            text="",
            color=MatrixTheme.YELLOW,
            fill=pg.mkBrush(MatrixTheme.BLACK, alpha=150)
        )
        chart.addItem(self.crosshair_label)

        # Initialize signal overlay
        if SIGNALS_AVAILABLE and SignalOverlay:
            self.signal_overlay = SignalOverlay(chart)
            print("✅ Signal overlay initialized")

        return chart
    
    def create_volume_chart(self) -> pg.PlotWidget:
        """Create volume chart below main chart"""
        volume_chart = pg.PlotWidget(
            background=MatrixTheme.BLACK,
            enableMenu=False
        )
        
        volume_chart.setLabel('left', 'Volume', color=MatrixTheme.GREEN)
        volume_chart.setFixedHeight(100)
        volume_chart.showGrid(x=True, y=True, alpha=0.3)
        
        # Set Matrix theme colors
        volume_chart.getAxis('left').setPen(MatrixTheme.GREEN)
        volume_chart.getAxis('bottom').setPen(MatrixTheme.GREEN)
        volume_chart.getAxis('left').setTextPen(MatrixTheme.GREEN)
        volume_chart.getAxis('bottom').setTextPen(MatrixTheme.GREEN)
        
        # Hide initially if volume is disabled
        if not self.show_volume:
            volume_chart.hide()
        
        return volume_chart
    
    def create_info_panel(self) -> QHBoxLayout:
        """Create chart information panel"""
        layout = QHBoxLayout()
        
        # Price info
        self.price_info = QLabel("Price: --")
        self.price_info.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        layout.addWidget(self.price_info)
        
        # Volume info
        self.volume_info = QLabel("Volume: --")
        self.volume_info.setStyleSheet(f"color: {MatrixTheme.GREEN};")
        layout.addWidget(self.volume_info)
        
        # Bid/Ask info
        self.bid_info = QLabel("Bid: --")
        self.bid_info.setStyleSheet(f"color: {MatrixTheme.GREEN};")
        layout.addWidget(self.bid_info)
        
        self.ask_info = QLabel("Ask: --")
        self.ask_info.setStyleSheet(f"color: {MatrixTheme.RED};")
        layout.addWidget(self.ask_info)
        
        layout.addStretch()
        
        # Chart instructions
        instructions = QLabel("💡 Left-click: BUY | Right-click: SELL")
        instructions.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: 10px;")
        layout.addWidget(instructions)
        
        return layout
    
    def update_ohlcv_data(self, ohlcv_data: List[List]):
        """Update chart with new OHLCV data"""
        try:
            if not ohlcv_data:
                return

            self.ohlcv_data = ohlcv_data

            # Debug: Check data quality (can be removed in production)
            if len(ohlcv_data) > 0:
                last_item = ohlcv_data[-1]
                import time
                current_time = time.time()
                last_ts = last_item[0] / 1000

                # Only warn if timestamps are significantly off
                if abs(current_time - last_ts) > 86400:  # More than 1 day difference
                    print(f"⚠ WARNING: Chart timestamp seems incorrect. Current: {current_time}, Chart: {last_ts}")

            # Extract data for plotting - use actual timestamps with validation
            import time
            current_time = time.time()

            # Filter out invalid timestamps (before 2020 or too far in future)
            min_timestamp = 1577836800  # Jan 1, 2020
            max_timestamp = current_time + 86400  # 1 day in future max

            valid_data = []
            for item in ohlcv_data:
                timestamp_seconds = item[0] / 1000
                if min_timestamp <= timestamp_seconds <= max_timestamp:
                    valid_data.append(item)
                else:
                    print(f"⚠ Filtered invalid timestamp: {timestamp_seconds} (converted from {item[0]})")

            if not valid_data:
                print("⚠ No valid timestamp data found, skipping chart update")
                return

            timestamps = [item[0] / 1000 for item in valid_data]  # Convert to seconds
            opens = [item[1] for item in valid_data]
            highs = [item[2] for item in valid_data]
            lows = [item[3] for item in valid_data]
            closes = [item[4] for item in valid_data]
            volumes = [item[5] for item in valid_data]

            # Clear main chart completely for exclusive chart types
            self.clear_chart_data()

            # Display chart based on selected type (exclusive)
            if self.chart_type == "Tick":
                # For tick chart, only show trade dots (no OHLCV data)
                # Trade dots are added separately via update_trade_data()
                pass  # Don't plot OHLCV data for tick chart
            elif self.chart_type == "Line":
                self.plot_line_chart(timestamps, closes)
            elif self.chart_type == "Candlestick":
                self.plot_enhanced_candlesticks(timestamps, opens, highs, lows, closes)

            # Update volume chart with enhanced styling (only for Line/Candlestick)
            if self.show_volume and volumes and self.chart_type != "Tick":
                self.plot_enhanced_volume(timestamps, opens, closes, volumes)

            # Update bid/ask lines
            if self.show_bid_ask:
                self.update_bid_ask_lines()

            # Update info panel
            if closes:
                self.update_info_panel(closes[-1], volumes[-1] if volumes else 0)

        except Exception as e:
            print(f"Error updating OHLCV data: {e}")
    
    def clear_chart_data(self):
        """Clear chart data based on chart type"""
        try:
            # Remove candlestick item
            if self.candlestick_item:
                self.chart_widget.removeItem(self.candlestick_item)
                self.candlestick_item = None

            # Clear volume bars
            for bar in self.volume_bars:
                self.volume_widget.removeItem(bar)
            self.volume_bars.clear()

            # For non-tick charts, clear trade dots to avoid overlap
            if self.chart_type != "Tick":
                # Clear trade dots for Line and Candlestick charts
                for trade_item in self.buy_trades:
                    self.chart_widget.removeItem(trade_item)
                for trade_item in self.sell_trades:
                    self.chart_widget.removeItem(trade_item)
                self.buy_trades.clear()
                self.sell_trades.clear()

        except Exception as e:
            print(f"Error clearing chart data: {e}")

    def plot_enhanced_candlesticks(self, timestamps, opens, highs, lows, closes):
        """Plot enhanced candlestick chart with professional styling"""
        try:
            # Skip PyQtGraph's CandlestickItem since it's not available in this version
            # Go directly to our custom implementation
            self.plot_candlesticks(timestamps, opens, highs, lows, closes)

        except Exception as e:
            print(f"Error plotting enhanced candlesticks: {e}")
            # Final fallback to line chart
            self.plot_line_chart(timestamps, closes)

    def plot_candlesticks(self, timestamps, opens, highs, lows, closes):
        """Plot candlestick chart (fallback implementation)"""
        try:
            # Use custom candlestick implementation
            self.candlestick_item = CandlestickChart()
            self.candlestick_item.set_data(timestamps, opens, highs, lows, closes)
            self.chart_widget.addItem(self.candlestick_item)
        except Exception as e:
            print(f"Error plotting candlesticks: {e}")
            # Fallback to line chart
            self.plot_line_chart(timestamps, closes)
    
    def plot_line_chart(self, timestamps, prices):
        """Plot line chart"""
        try:
            pen = pg.mkPen(color=MatrixTheme.GREEN, width=2)
            self.chart_widget.plot(timestamps, prices, pen=pen)
        except Exception as e:
            print(f"Error plotting line chart: {e}")
    
    def plot_ohlc_bars(self, timestamps, opens, highs, lows, closes):
        """Plot OHLC bar chart"""
        try:
            # Simple OHLC implementation using lines
            for i, (ts, o, h, l, c) in enumerate(zip(timestamps, opens, highs, lows, closes)):
                color = MatrixTheme.GREEN if c >= o else MatrixTheme.RED
                
                # High-low line
                self.chart_widget.plot([ts, ts], [l, h], pen=pg.mkPen(color, width=1))
                
                # Open tick
                self.chart_widget.plot([ts - 30, ts], [o, o], pen=pg.mkPen(color, width=1))
                
                # Close tick
                self.chart_widget.plot([ts, ts + 30], [c, c], pen=pg.mkPen(color, width=1))
                
        except Exception as e:
            print(f"Error plotting OHLC bars: {e}")
    
    def plot_enhanced_volume(self, timestamps, opens, closes, volumes):
        """Plot enhanced volume bars with color coding based on price movement"""
        try:
            self.volume_widget.clear()
            self.volume_bars.clear()

            if not volumes or len(volumes) == 0:
                return

            # Calculate bar width based on time interval
            width = (timestamps[1] - timestamps[0]) * 0.8 if len(timestamps) > 1 else 60

            # Create volume bars with colors based on price movement (like stable version)
            for i, (ts, vol) in enumerate(zip(timestamps, volumes)):
                # Determine if price went up or down
                is_up = closes[i] >= opens[i] if i < len(opens) and i < len(closes) else True
                color = MatrixTheme.GREEN if is_up else MatrixTheme.RED

                # Create volume bar
                bar = pg.BarGraphItem(
                    x=[ts],
                    height=[vol],
                    width=width,
                    brush=pg.mkBrush(color, alpha=150),  # Semi-transparent
                    pen=pg.mkPen(color, width=1)
                )
                self.volume_widget.addItem(bar)
                self.volume_bars.append(bar)

            print(f"Plotted {len(volumes)} volume bars")

        except Exception as e:
            print(f"Error plotting enhanced volume: {e}")
            # Fallback to simple volume
            self.plot_volume(timestamps, volumes)

    def plot_volume(self, timestamps, volumes):
        """Plot volume bars (fallback implementation)"""
        try:
            self.volume_widget.clear()

            # Create volume bars
            width = (timestamps[1] - timestamps[0]) * 0.8 if len(timestamps) > 1 else 60

            for ts, vol in zip(timestamps, volumes):
                bar = pg.BarGraphItem(
                    x=[ts], height=[vol], width=width,
                    brush=pg.mkBrush(MatrixTheme.GREEN, alpha=100)
                )
                self.volume_widget.addItem(bar)

        except Exception as e:
            print(f"Error plotting volume: {e}")
    
    def update_bid_ask_lines(self):
        """Update bid/ask price lines"""
        try:
            # Remove existing lines
            if self.bid_line:
                self.chart_widget.removeItem(self.bid_line)
            if self.ask_line:
                self.chart_widget.removeItem(self.ask_line)
            
            # Add new lines if data available
            if self.bid_ask_data["bid"]:
                self.bid_line = pg.InfiniteLine(
                    pos=self.bid_ask_data["bid"],
                    angle=0,
                    pen=pg.mkPen(MatrixTheme.GREEN, width=1, style=pg.QtCore.Qt.DashLine)
                )
                self.chart_widget.addItem(self.bid_line)
            
            if self.bid_ask_data["ask"]:
                self.ask_line = pg.InfiniteLine(
                    pos=self.bid_ask_data["ask"],
                    angle=0,
                    pen=pg.mkPen(MatrixTheme.RED, width=1, style=pg.QtCore.Qt.DashLine)
                )
                self.chart_widget.addItem(self.ask_line)
                
        except Exception as e:
            print(f"Error updating bid/ask lines: {e}")
    
    def update_bid_ask_data(self, bid: float, ask: float):
        """Update bid/ask data"""
        self.bid_ask_data = {"bid": bid, "ask": ask}

        # Update info panel
        self.bid_info.setText(f"Bid: {bid:.6f}" if bid else "Bid: --")
        self.ask_info.setText(f"Ask: {ask:.6f}" if ask else "Ask: --")

    def update_trade_data(self, price: float, volume: float, side: str, timestamp: float):
        """Update with individual trade data"""
        try:
            # Validate timestamp (filter out 1970 and other invalid dates)
            import time
            current_time = time.time()
            min_timestamp = 1577836800  # Jan 1, 2020
            max_timestamp = current_time + 3600  # 1 hour in future max

            if not (min_timestamp <= timestamp <= max_timestamp):
                print(f"⚠ Filtered invalid trade timestamp: {timestamp}")
                return

            # Store the trade
            trade = {
                'price': price,
                'volume': volume,
                'side': side,  # 'buy' or 'sell'
                'timestamp': timestamp
            }
            self.trade_data.append(trade)

            # Keep only recent trades (last 1000)
            if len(self.trade_data) > 1000:
                self.trade_data = self.trade_data[-1000:]

            # Update latest price and volume
            self.latest_price = price
            self.latest_volume = volume

            # Update info panel with latest trade
            self.update_info_panel(price, volume)

            # Add trade point to chart only for Tick chart type
            if self.chart_type == "Tick":
                self.add_trade_point(price, timestamp, side)

        except Exception as e:
            print(f"Error updating trade data: {e}")

    def add_trade_point(self, price: float, timestamp: float, side: str):
        """Add a trade point to the chart using the stable version approach"""
        try:
            # Use the approach from me2_stable.py - direct chart.plot() method
            # This is more reliable than ScatterPlotItem

            if side == 'buy':
                # Plot buy trade as green dot
                plot_item = self.chart_widget.plot(
                    [timestamp],
                    [price],
                    pen=None,
                    symbol='o',
                    symbolPen=None,
                    symbolBrush='#00ff00',  # Green for buys
                    symbolSize=8
                )
                self.buy_trades.append(plot_item)
            else:
                # Plot sell trade as red dot
                plot_item = self.chart_widget.plot(
                    [timestamp],
                    [price],
                    pen=None,
                    symbol='o',
                    symbolPen=None,
                    symbolBrush='#ff0000',  # Red for sells
                    symbolSize=8
                )
                self.sell_trades.append(plot_item)

            # Limit number of trade points displayed (keep last 100)
            max_trades = 100
            if len(self.buy_trades) > max_trades:
                old_item = self.buy_trades.pop(0)
                self.chart_widget.removeItem(old_item)
            if len(self.sell_trades) > max_trades:
                old_item = self.sell_trades.pop(0)
                self.chart_widget.removeItem(old_item)

            # Removed debug print for cleaner output
            # print(f"Added {side} trade dot at price {price:.6f}, time {timestamp}")

            # Generate ML/LLM signal periodically (every 10th trade to avoid spam)
            if SIGNALS_AVAILABLE and len(self.trade_data) % 10 == 0:
                self.generate_trading_signal(price, timestamp)

        except Exception as e:
            print(f"Error adding trade point: {e}")

    def generate_trading_signal(self, current_price: float, timestamp: float):
        """Generate and display ML/LLM trading signal"""
        try:
            if not self.signal_manager or not self.signal_overlay:
                return

            # Convert trade data to DataFrame for ML analysis
            if len(self.trade_data) < 20:  # Need minimum data
                return

            import pandas as pd

            # Create simple OHLCV data from trades (simplified)
            df = pd.DataFrame(self.trade_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df = df.set_index('timestamp')

            # Resample to 1-minute bars (simplified OHLCV)
            ohlcv = df['price'].resample('1T').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()

            # Add volume
            ohlcv['volume'] = df['volume'].resample('1T').sum().fillna(0)

            if len(ohlcv) < 10:  # Need minimum bars
                return

            # Generate signal asynchronously (mock for now)
            import asyncio

            async def generate_signal():
                try:
                    signal = await self.signal_manager.generate_comprehensive_signal(ohlcv, current_price)

                    # Add signal to chart overlay
                    if signal and self.signal_overlay:
                        self.signal_overlay.add_signal(signal, timestamp, current_price)
                        print(f"🤖 Generated {signal.signal_type.value} signal with {signal.confidence:.1%} confidence")

                except Exception as e:
                    print(f"Error generating signal: {e}")

            # Run signal generation (simplified - in production would use proper async handling)
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Create task for running loop
                    asyncio.create_task(generate_signal())
                else:
                    # Run directly if no loop
                    loop.run_until_complete(generate_signal())
            except:
                # Fallback: run synchronously
                print("🤖 Generating mock signal...")
                from ...ml.signal_generator import TradingSignal, SignalType, SignalStrength
                import random

                # Create mock signal
                signal_types = [SignalType.BUY, SignalType.SELL, SignalType.HOLD]
                mock_signal = TradingSignal(
                    signal_type=random.choice(signal_types),
                    confidence=random.uniform(0.6, 0.9),
                    strength=SignalStrength.MODERATE,
                    price_target=current_price * random.uniform(0.98, 1.02),
                    reasoning="Mock ML signal for demonstration"
                )

                if self.signal_overlay:
                    self.signal_overlay.add_signal(mock_signal, timestamp, current_price)
                    print(f"🤖 Generated mock {mock_signal.signal_type.value} signal")

        except Exception as e:
            print(f"Error in signal generation: {e}")
    
    def update_info_panel(self, price: float, volume: float):
        """Update information panel"""
        if price and price > 0:
            self.price_info.setText(f"Price: {price:.6f}")
        else:
            self.price_info.setText("Price: --")

        if volume and volume > 0:
            self.volume_info.setText(f"Volume: {volume:,.2f}")
        else:
            self.volume_info.setText("Volume: --")
    
    def update_connection_status(self, connected: bool):
        """Update connection status display"""
        if connected:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        else:
            self.status_label.setText("Disconnected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
    
    def update_display(self):
        """Update display elements (called by timer)"""
        # Update crosshair and other dynamic elements
        pass
    
    # Event handlers
    def on_symbol_changed(self, symbol: str):
        """Handle symbol change"""
        self.current_symbol = symbol
        self.symbol_changed.emit(symbol)
    
    def on_timeframe_changed(self, timeframe: str):
        """Handle timeframe change"""
        self.current_timeframe = timeframe
        self.timeframe_changed.emit(timeframe)
    
    def on_chart_type_changed(self, chart_type: str):
        """Handle chart type change"""
        self.chart_type = chart_type

        # Clear all chart data first
        self.clear_chart_data()

        # Refresh chart with current data based on new type
        if chart_type == "Tick":
            # For tick chart, clear OHLCV display and show only trade dots
            # Trade dots will be added as new trades come in
            print(f"Switched to Tick chart - showing only trade dots")
        else:
            # For Line/Candlestick charts, refresh with OHLCV data
            if self.ohlcv_data:
                self.update_ohlcv_data(self.ohlcv_data)
            print(f"Switched to {chart_type} chart - showing OHLCV data")
    
    def on_volume_toggle(self, state):
        """Handle volume display toggle"""
        self.show_volume = bool(state)
        if self.show_volume:
            self.volume_widget.show()
        else:
            self.volume_widget.hide()
    
    def on_bid_ask_toggle(self, state):
        """Handle bid/ask display toggle"""
        self.show_bid_ask = bool(state)
        if not self.show_bid_ask:
            if self.bid_line:
                self.chart_widget.removeItem(self.bid_line)
            if self.ask_line:
                self.chart_widget.removeItem(self.ask_line)
    
    def on_chart_clicked(self, event):
        """Handle chart click events"""
        try:
            if event.button() == 1:  # Left click
                pos = event.pos()
                view_box = self.chart_widget.getViewBox()
                scene_pos = view_box.mapSceneToView(pos)
                
                price = scene_pos.y()
                timestamp = scene_pos.x()
                
                self.chart_clicked.emit(price, timestamp)
                
        except Exception as e:
            print(f"Error handling chart click: {e}")
