2025-06-18 15:00:22,447 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:00:22,467 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:00:22,468 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:00:22,468 - main - INFO - Performance monitoring initialized
2025-06-18 15:00:22,478 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:00:22,478 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:00:22,480 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:00:23,724 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:00:24,869 - websocket - INFO - Websocket connected
2025-06-18 15:00:27,541 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:00:27,542 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:00:27,542 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:00:27,543 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:00:27,611 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:00:29,678 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:00:29,679 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:00:29,679 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:00:29,682 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:00:29,682 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:00:29,720 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:00:29,721 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:00:29,721 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:00:29,728 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_150029_c68bc157
2025-06-18 15:00:29,731 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_150029_c68bc157
2025-06-18 15:03:13,895 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:03:13,911 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:03:13,911 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:03:13,912 - main - INFO - Performance monitoring initialized
2025-06-18 15:03:13,919 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:03:13,920 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:03:13,921 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:03:15,141 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:03:16,164 - websocket - INFO - Websocket connected
2025-06-18 15:03:17,817 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:03:17,818 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:03:17,819 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:03:17,819 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:03:17,884 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:03:19,944 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:03:19,945 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:03:19,945 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:03:19,949 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:03:19,949 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:03:19,958 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:03:19,961 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:03:19,961 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:03:19,966 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_150319_842aa410
2025-06-18 15:03:19,971 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_150319_842aa410
2025-06-18 15:21:10,833 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:21:10,849 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:21:10,849 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:21:10,850 - main - INFO - Performance monitoring initialized
2025-06-18 15:21:10,858 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:21:10,858 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:21:10,859 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:21:15,371 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:21:16,422 - websocket - INFO - Websocket connected
2025-06-18 15:21:18,881 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:21:18,882 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:21:18,882 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:21:18,883 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:21:18,889 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:21:20,947 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:21:20,948 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:21:20,948 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:21:20,951 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:21:20,952 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:21:20,960 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:21:20,961 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:21:20,962 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:21:20,969 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152120_234ad650
2025-06-18 15:21:20,973 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152120_234ad650
2025-06-18 15:22:07,972 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:22:07,990 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:22:07,990 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:22:07,991 - main - INFO - Performance monitoring initialized
2025-06-18 15:22:08,001 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:22:08,002 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:22:08,003 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:22:12,709 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:22:13,605 - websocket - INFO - Websocket connected
2025-06-18 15:22:15,826 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:22:15,826 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:22:15,827 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:22:15,827 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:22:15,842 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:22:17,893 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:22:17,893 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:22:17,894 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:22:17,898 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:22:17,898 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:22:17,907 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:22:17,909 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:22:17,910 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:22:17,920 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152217_cc54e5d3
2025-06-18 15:22:17,923 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152217_cc54e5d3
2025-06-18 15:24:32,480 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:24:32,499 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:24:32,500 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:24:32,501 - main - INFO - Performance monitoring initialized
2025-06-18 15:24:32,508 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:24:32,509 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:24:32,510 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:24:37,316 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:24:38,194 - websocket - INFO - Websocket connected
2025-06-18 15:24:40,543 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:24:40,544 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:24:40,544 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:24:40,545 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:24:40,551 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:24:42,602 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:24:42,602 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:24:42,603 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:24:42,605 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:24:42,606 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:24:42,614 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:24:42,614 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:24:42,615 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:24:42,624 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152442_6e4b1cc3
2025-06-18 15:24:42,627 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152442_6e4b1cc3
2025-06-18 15:29:13,325 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:29:13,340 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:29:13,340 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:29:13,342 - main - INFO - Performance monitoring initialized
2025-06-18 15:29:13,349 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:29:13,349 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:29:13,350 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:29:19,499 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:29:20,370 - websocket - INFO - Websocket connected
2025-06-18 15:29:22,605 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:29:22,605 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:29:22,606 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:29:22,607 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:29:22,612 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:29:24,673 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:29:24,673 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:29:24,675 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:29:24,677 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:29:24,677 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:29:24,687 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:29:24,688 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:29:24,689 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:29:24,697 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152924_5c416754
2025-06-18 15:29:24,700 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152924_5c416754
2025-06-18 15:33:42,818 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:33:42,834 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:33:42,834 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:33:42,836 - main - INFO - Performance monitoring initialized
2025-06-18 15:33:42,844 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:33:42,844 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:33:42,845 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:33:47,060 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:33:48,490 - websocket - INFO - Websocket connected
2025-06-18 15:33:50,869 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:33:50,870 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:33:50,870 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:33:50,870 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:33:50,875 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:33:52,959 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:33:52,960 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:33:52,960 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:33:52,964 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:33:52,965 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:33:52,972 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:33:52,973 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:33:52,974 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:33:52,983 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_153352_b9816e98
2025-06-18 15:33:52,986 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_153352_b9816e98
2025-06-18 15:41:43,413 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:41:43,433 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:41:43,434 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:41:43,434 - main - INFO - Performance monitoring initialized
2025-06-18 15:41:43,443 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:41:43,444 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:41:43,445 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:41:47,705 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:41:48,851 - websocket - INFO - Websocket connected
2025-06-18 15:41:51,286 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:41:51,286 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:41:51,286 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:41:51,287 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:41:51,292 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:41:53,347 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:41:53,348 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:41:53,349 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:41:53,351 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:41:53,351 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:41:53,361 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:41:53,363 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:41:53,363 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:41:53,369 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_154153_259ebf1f
2025-06-18 15:41:53,372 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_154153_259ebf1f
2025-06-18 15:50:18,042 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:50:18,062 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:50:18,063 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:50:18,063 - main - INFO - Performance monitoring initialized
2025-06-18 15:50:18,072 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:50:18,072 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:50:18,073 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:50:22,297 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:50:23,197 - websocket - INFO - Websocket connected
2025-06-18 15:50:24,994 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:50:24,994 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:50:24,995 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:50:24,996 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:50:25,002 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:50:27,058 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:50:27,059 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:50:27,060 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:50:27,062 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:50:27,063 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:50:27,071 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:50:27,073 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:50:27,073 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:50:27,081 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155027_81b1dc90
2025-06-18 15:50:27,084 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155027_81b1dc90
2025-06-18 15:54:31,828 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:54:31,842 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:54:31,842 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:54:31,844 - main - INFO - Performance monitoring initialized
2025-06-18 15:54:31,852 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:54:31,852 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:54:31,853 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:54:36,486 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:54:37,531 - websocket - INFO - Websocket connected
2025-06-18 15:54:39,761 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:54:39,762 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:54:39,762 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:54:39,763 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:54:39,768 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:54:41,836 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:54:41,836 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:54:41,837 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:54:41,840 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:54:41,840 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:54:41,849 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:54:41,851 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:54:41,851 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:54:41,859 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155441_ba5d3153
2025-06-18 15:54:41,862 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155441_ba5d3153
2025-06-18 15:56:21,647 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:56:21,665 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:56:21,665 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:56:21,666 - main - INFO - Performance monitoring initialized
2025-06-18 15:56:21,674 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:56:21,675 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:56:21,676 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:56:25,651 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:56:26,622 - websocket - INFO - Websocket connected
2025-06-18 15:56:28,771 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:56:28,772 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:56:28,772 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:56:28,773 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:56:28,778 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:56:30,837 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:56:30,837 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:56:30,838 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:56:30,840 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:56:30,840 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:56:30,848 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:56:30,849 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:56:30,849 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:56:30,855 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155630_bf7572c4
2025-06-18 15:56:30,858 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155630_bf7572c4
2025-06-18 15:59:00,639 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:59:00,654 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:59:00,655 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:59:00,657 - main - INFO - Performance monitoring initialized
2025-06-18 15:59:00,665 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:59:00,665 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:59:00,666 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:59:04,646 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:59:05,683 - websocket - INFO - Websocket connected
2025-06-18 15:59:08,550 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:59:08,551 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:59:08,551 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:59:08,552 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:59:08,556 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:59:10,628 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:59:10,628 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:59:10,630 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:59:10,632 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:59:10,633 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:59:10,641 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:59:10,642 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:59:10,643 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:59:10,652 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155910_1d25eaae
2025-06-18 15:59:10,656 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155910_1d25eaae
2025-06-18 16:01:35,516 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:01:35,538 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:01:35,539 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:01:35,539 - main - INFO - Performance monitoring initialized
2025-06-18 16:01:35,551 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:01:35,552 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:01:35,554 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:01:39,721 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:01:40,949 - websocket - INFO - Websocket connected
2025-06-18 16:01:43,594 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:01:43,595 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:01:43,596 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:01:43,596 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:01:43,602 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:01:45,665 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:01:45,665 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:01:45,666 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:01:45,668 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:01:45,668 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:01:45,676 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:01:45,677 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:01:45,678 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:01:45,686 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_160145_bee4b7f2
2025-06-18 16:01:45,688 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_160145_bee4b7f2
2025-06-18 16:04:23,042 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:04:23,057 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:04:23,058 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:04:23,058 - main - INFO - Performance monitoring initialized
2025-06-18 16:04:23,066 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:04:23,066 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:04:23,068 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:04:28,056 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:04:29,675 - websocket - INFO - Websocket connected
2025-06-18 16:04:32,893 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:04:32,894 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:04:32,895 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:04:32,896 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:04:32,901 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:04:34,969 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:04:34,969 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:04:34,971 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:04:34,973 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:04:34,973 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:04:34,981 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:04:34,983 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:04:34,984 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:04:34,989 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_160434_9a69d9ee
2025-06-18 16:04:34,993 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_160434_9a69d9ee
2025-06-18 16:25:44,318 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:25:44,331 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:25:44,332 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:25:44,333 - main - INFO - Performance monitoring initialized
2025-06-18 16:25:44,341 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:25:44,341 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:25:44,342 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:25:49,485 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:25:50,409 - websocket - INFO - Websocket connected
2025-06-18 16:25:53,202 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:25:53,202 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:25:53,202 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:25:53,203 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:25:53,211 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:25:55,285 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:25:55,286 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:25:55,286 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:27:58,487 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:27:58,504 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:27:58,505 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:27:58,505 - main - INFO - Performance monitoring initialized
2025-06-18 16:27:58,514 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:27:58,514 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:27:58,515 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:28:02,733 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:28:03,936 - websocket - INFO - Websocket connected
2025-06-18 16:28:06,236 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:28:06,236 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:28:06,236 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:28:06,236 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:28:06,242 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:28:08,305 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:28:08,306 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:28:08,307 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:34:24,223 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:34:24,244 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:34:24,245 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:34:24,246 - main - INFO - Performance monitoring initialized
2025-06-18 16:34:24,263 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:34:24,263 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:34:24,266 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:34:29,446 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:34:30,348 - websocket - INFO - Websocket connected
2025-06-18 16:34:32,632 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:34:32,633 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:34:32,633 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:34:32,634 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:34:32,640 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:34:34,696 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:34:34,696 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:34:34,697 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:34:34,699 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:34:34,699 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:34:34,709 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:34:34,710 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:34:34,711 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:34:34,718 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_163434_5e88f5ef
2025-06-18 16:34:34,720 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_163434_5e88f5ef
2025-06-18 16:38:20,125 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 16:38:20,125 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 16:38:20,126 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.170431
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 16:38:20,126 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 16:38:29,034 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 882 chars
2025-06-18 16:38:29,035 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT | CONFIDENCE: 85% | REASONING: The ML predictions consistently advise a 'WAIT' position with high confidence (60%). This suggests that the market is currently in a volatile state, and it may be beneficial to avoid making any immediate trades. Instead, traders should monitor DOGE/USDT ...
2025-06-18 16:38:29,035 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 199, Total: 468
2025-06-18 16:38:29,037 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 16:38:29,037 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 16:38:29,037 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.170431
📈 Bid: $0.170431 | Ask: $0.170491 | Spread: $0.000060
⏰ Analysis Time: 2025-06-18 16:38:29 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 16:38:29,038 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 16:38:37,612 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1624 chars
2025-06-18 16:38:37,612 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: SHORT
CONFIDENCE: 70%
POSITION_SIZE: SMALL
ENTRY_STRATEGY: IMMEDIATE (with a secondary condition of WAIT_FOR_BREAKOUT)
RISK_LEVEL: MEDIUM
CREATIVE_INSIGHT: "Even in the crypto world, there's more to volatility than just price action—it’s about sentiment and narrative. The current mar...
2025-06-18 16:38:37,613 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1158, Completion: 399, Total: 1557
2025-06-18 16:39:10,614 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 16:39:10,615 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 16:39:10,615 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.170337
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 16:39:10,616 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 16:39:15,399 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 764 chars
2025-06-18 16:39:15,400 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT
CONFIDENCE: 65%
REASONING: The ML model predictions are consistently suggesting to 'WAIT'. Despite the current price being $0.170337 for DOGE/USDT, which might seem appealing at first glance due to its relatively low value compared to other cryptocurrencies like Bitcoin or Ethereum; h...
2025-06-18 16:39:15,401 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 181, Total: 450
2025-06-18 16:39:15,403 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 16:39:15,403 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 16:39:15,404 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.170337
📈 Bid: $0.170337 | Ask: $0.170427 | Spread: $0.000090
⏰ Analysis Time: 2025-06-18 16:39:15 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 16:39:15,405 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 16:39:21,800 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 947 chars
2025-06-18 16:39:21,801 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: LONG (85% confidence)
CONFIDENCE: 90%
POSITION_SIZE: MEDIUM
ENTRY_STRATEGY: IMMEDIATE
RISK_LEVEL: HIGH
CREATIVE_INSIGHT: The ebbs and flows of the DOGE/USDT market, much like a symphony's crescendo, may hold hidden melodies for those willing to listen closely. 
SYNTHESIS: Despite ML ...
2025-06-18 16:39:21,801 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1182, Completion: 249, Total: 1431
2025-06-18 16:39:55,236 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 16:39:55,237 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 16:39:55,237 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.170550
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 16:39:55,238 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 16:39:59,222 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 502 chars
2025-06-18 16:39:59,222 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT  
CONFIDENCE: 70%    
REASONING: Given the ML predictions all are indicating 'WAIT' with a high confidence of 60%, and considering that cryptocurrency markets can be highly volatile, it is prudent to adopt a wait-and-see approach. The current price does not indicate an immediate oppor...
2025-06-18 16:39:59,223 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 122, Total: 391
2025-06-18 16:39:59,225 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 16:39:59,225 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 16:39:59,226 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.170550
📈 Bid: $0.170550 | Ask: $0.170551 | Spread: $0.000001
⏰ Analysis Time: 2025-06-18 16:39:59 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 16:39:59,226 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 16:40:02,370 - websocket - ERROR - [WinError 10054] An existing connection was forcibly closed by the remote host - goodbye
2025-06-18 16:40:05,396 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 919 chars
2025-06-18 16:40:05,397 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: WAIT  
CONFIDENCE: 85%   
POSITION_SIZE: SMALL    
ENTRY_STRATEGY: IMMEDIATE   
RISK_LEVEL: HIGH    
CREATIVE_INSIGHT: In a universe where the moon's gravitational pull affects digital assets, DOGE/USDT might be experiencing lunar-inspired market volatility.  
SYNTHESIS: The consensu...
2025-06-18 16:40:05,398 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1177, Completion: 238, Total: 1415
2025-06-18 16:48:17,467 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:48:17,485 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:48:17,485 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:48:17,487 - main - INFO - Performance monitoring initialized
2025-06-18 16:48:17,498 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:48:17,500 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:48:17,501 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:48:25,964 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:48:27,368 - websocket - INFO - Websocket connected
2025-06-18 16:48:30,699 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:48:30,699 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:48:30,700 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:48:30,700 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:48:30,707 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:48:32,784 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:48:32,785 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:48:32,785 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:48:32,791 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:48:32,791 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:48:32,805 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:48:32,807 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:48:32,808 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:48:32,814 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_164832_21a16b3a
2025-06-18 16:48:32,818 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_164832_21a16b3a
2025-06-18 16:50:38,568 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:50:38,587 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:50:38,588 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:50:38,589 - main - INFO - Performance monitoring initialized
2025-06-18 16:50:38,599 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:50:38,599 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:50:38,601 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:50:44,712 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:50:46,224 - websocket - INFO - Websocket connected
2025-06-18 16:50:50,870 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:50:50,871 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:50:50,871 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:50:50,872 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:50:50,877 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:50:52,914 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:50:52,915 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:50:52,915 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:50:52,918 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:50:52,919 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:50:52,928 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:50:52,931 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:50:52,932 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:50:52,941 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_165052_d4bacaa4
2025-06-18 16:50:52,944 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_165052_d4bacaa4
2025-06-18 17:07:56,943 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:07:56,943 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:07:56,944 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.171045
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 17:07:56,944 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 17:08:00,861 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 480 chars
2025-06-18 17:08:00,861 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT  
CONFIDENCE: 60%  
REASONING: The current price of DOGE/USDT at $0.171045 is relatively low, and the majority of ML predictions are to 'WAIT', indicating a consensus among models that there may not be an immediate opportunity for significant profit without additional market analysis ...
2025-06-18 17:08:00,862 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 119, Total: 388
2025-06-18 17:08:00,863 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:08:00,863 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:08:00,864 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.171045
📈 Bid: $0.171045 | Ask: $0.171046 | Spread: $0.000001
⏰ Analysis Time: 2025-06-18 17:08:00 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 17:08:00,864 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 17:08:07,565 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1012 chars
2025-06-18 17:08:07,566 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: LONG  
CONFIDENCE: 78%  
POSITION_SIZE: MEDIUM  
ENTRY_STRATEGY: WAIT_FOR_BREAKOUT  
RISK_LEVEL: HIGH  
CREATIVE_INSIGHT: "In a world where digital dragons hoard cryptocurrency, the DOGE/USDT phoenix rises from ashes to seek treasure. Seize this moment before it soars higher."  
SYNT...
2025-06-18 17:08:07,566 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1184, Completion: 276, Total: 1460
2025-06-18 17:08:40,863 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:08:40,864 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:08:40,865 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.171219
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 17:08:40,865 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 17:08:44,811 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 525 chars
2025-06-18 17:08:44,811 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT | CONFIDENCE: 65% | REASONING: The ML predictions indicate a consensus to wait, with confidence levels all at 60%. While the current price does not provide an immediate signal for entry or exit positions based solely on this information. However, considering there's no clear trend and...
2025-06-18 17:08:44,813 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 117, Total: 386
2025-06-18 17:08:44,819 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:08:44,821 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:08:44,822 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.171219
📈 Bid: $0.171219 | Ask: $0.171220 | Spread: $0.000001
⏰ Analysis Time: 2025-06-18 17:08:44 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 17:08:44,823 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 17:08:52,520 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1382 chars
2025-06-18 17:08:52,521 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: SHORT (85.0% confidence)
CONFIDENCE: 85
POSITION_SIZE: SMALL
ENTRY_STRATEGY: WAIT_FOR_DIP
RISK_LEVEL: MEDIUM
CREATIVE_INSIGHT: In a market where the emotions of fear and uncertainty are prevalent, shorting DOGE/USDT represents not just an opportunity to capitalize on price drops but ...
2025-06-18 17:08:52,522 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1155, Completion: 338, Total: 1493
2025-06-18 17:09:25,990 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:09:25,991 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:09:25,991 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.171165
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 17:09:25,993 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 17:09:29,948 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 468 chars
2025-06-18 17:09:29,950 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT | CONFIDENCE: 60% | REASONING: The ML predictions indicate a consensus to 'WAIT', which suggests the market may be in a state of equilibrium or volatility that could last for some time. Despite low confidence levels from these models, it's prudent not to take an aggressive position wi...
2025-06-18 17:09:29,952 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 113, Total: 382
2025-06-18 17:09:29,955 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:09:29,956 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:09:29,957 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.171165
📈 Bid: $0.171165 | Ask: $0.171222 | Spread: $0.000057
⏰ Analysis Time: 2025-06-18 17:09:29 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 17:09:29,959 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 17:09:38,465 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1622 chars
2025-06-18 17:09:38,465 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: WAIT/SHORT
CONFIDENCE: 65%
POSITION_SIZE: SMALL
ENTRY_STRATEGY: GRADUAL
RISK_LEVEL: MEDIUM
CREATIVE_INSIGHT: The market's indecision, akin to an artist pondering the right brush stroke before painting on canvas. 
SYNTHESIS: Given that all models and analyses suggest caution ('WAIT'),...
2025-06-18 17:09:38,466 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1156, Completion: 404, Total: 1560
2025-06-18 17:10:11,879 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:10:11,880 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:10:11,881 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.170870
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 17:10:11,881 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 17:10:15,730 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 481 chars
2025-06-18 17:10:15,731 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT
CONFIDENCE: 65%
REASONING: Given the ML predictions all suggest a 'WAIT' decision with equal confidence, it indicates that there is no clear consensus on whether DOGE/USDT will appreciate or depreciate in price. This lack of direction and low volatility suggests holding off for now to...
2025-06-18 17:10:15,731 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 110, Total: 379
2025-06-18 17:10:15,736 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:10:15,737 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:10:15,738 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.170870
📈 Bid: $0.170870 | Ask: $0.170927 | Spread: $0.000057
⏰ Analysis Time: 2025-06-18 17:10:15 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 17:10:15,739 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 17:10:23,955 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1524 chars
2025-06-18 17:10:23,955 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: SHORT
CONFIDENCE: 85%
POSITION_SIZE: LARGE
ENTRY_STRATEGY: GRADUAL
RISK_LEVEL: HIGH
CREATIVE_INSIGHT: The market's volatility may be akin to the unpredictable nature of light refracting through prism – diverse angles leading to sudden, sharp shifts. 
SYNTHESIS: Despite all models sug...
2025-06-18 17:10:23,955 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1176, Completion: 375, Total: 1551
2025-06-18 17:10:56,968 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:10:56,969 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:10:56,969 - llama.lmstudio_runner - INFO - 💬 User Prompt: Analyze the current market conditions for DOGE/USDT:USDT:

Current Price: 0.170655
ML Predictions: ['WAIT', 'WAIT', 'WAIT']
ML Confidences: ['60.0%', '60.0%', '60.0%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]...
2025-06-18 17:10:56,970 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.7, Max Tokens: 200
2025-06-18 17:11:01,007 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 497 chars
2025-06-18 17:11:01,009 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: WAIT
CONFIDENCE: 70%
REASONING: The current market predictions from the ML models are all suggesting a 'WAIT' action, indicating uncertainty or no clear trend. While there is some confidence (60%) in these recommendations which could be seen as an opportunity for potential gains if conditi...
2025-06-18 17:11:01,012 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 269, Completion: 121, Total: 390
2025-06-18 17:11:01,018 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-06-18 17:11:01,019 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:

DECISION: [LONG/SHORT/WAIT]
CONFIDENC...
2025-06-18 17:11:01,020 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: DOGE/USDT:USDT
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: $0.170655
📈 Bid: $0.170655 | Ask: $0.170698 | Spread: $0.000043
⏰ Analysis Time: 2025-06-18 17:11:01 UTC

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANAL...
2025-06-18 17:11:01,021 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.8, Max Tokens: 500
2025-06-18 17:11:08,722 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1351 chars
2025-06-18 17:11:08,722 - llama.lmstudio_runner - INFO - 📄 Response Preview: FINAL_DECISION: SHORT
CONFIDENCE: 80%
POSITION_SIZE: MEDIUM
ENTRY_STRATEGY: GRADUAL
RISK_LEVEL: HIGH
CREATIVE_INSIGHT: The Epinnox AI Trading System's current stance on the DOGE/USDT pair reflects a sophisticated blend of cautious optimism and technological foresight, indicating potential short-term...
2025-06-18 17:11:08,724 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 1171, Completion: 345, Total: 1516
2025-06-18 17:11:30,109 - main - INFO - Epinnox v6 starting up...
2025-06-18 17:11:30,126 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 17:11:30,126 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 17:11:30,126 - main - INFO - Performance monitoring initialized
2025-06-18 17:11:30,133 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 17:11:30,134 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 17:11:30,135 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 17:11:36,468 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 17:11:38,019 - websocket - INFO - Websocket connected
2025-06-18 17:11:41,953 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 17:11:41,953 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 17:11:41,953 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 17:11:41,954 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 17:11:41,958 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 17:11:44,004 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 17:11:44,006 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 17:11:44,006 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 17:11:44,008 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 17:11:44,009 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 17:11:44,018 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 17:11:44,019 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 17:11:44,020 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 17:11:44,029 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_171144_a9694dd1
2025-06-18 17:11:44,031 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_171144_a9694dd1
2025-06-18 17:12:06,060 - main - INFO - Epinnox v6 starting up...
2025-06-18 17:12:06,074 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 17:12:06,074 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 17:12:06,075 - main - INFO - Performance monitoring initialized
2025-06-18 17:12:06,082 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 17:12:06,083 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 17:12:06,084 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 17:12:12,393 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 17:12:13,902 - websocket - INFO - Websocket connected
2025-06-18 17:12:18,905 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 17:12:18,906 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 17:12:18,906 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 17:12:18,907 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 17:12:18,911 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 17:12:20,969 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 17:12:20,969 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 17:12:20,970 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 17:12:20,972 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 17:12:20,973 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 17:12:20,982 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 17:12:20,983 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 17:12:20,983 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 17:12:20,991 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_171220_f112f6a1
2025-06-18 17:12:20,993 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_171220_f112f6a1
2025-06-18 17:14:33,525 - main - INFO - Epinnox v6 starting up...
2025-06-18 17:14:33,541 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 17:14:33,541 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 17:14:33,542 - main - INFO - Performance monitoring initialized
2025-06-18 17:14:33,550 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 17:14:33,550 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 17:14:33,552 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 17:14:41,207 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 17:14:42,690 - websocket - INFO - Websocket connected
2025-06-18 17:14:47,660 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 17:14:47,660 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 17:14:47,661 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 17:14:47,661 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 17:14:47,666 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 17:14:49,752 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 17:14:49,753 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 17:14:49,753 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 17:14:49,755 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 17:14:49,756 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 17:14:49,764 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 17:14:49,766 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 17:14:49,766 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 17:14:49,772 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_171449_fad52bc3
2025-06-18 17:14:49,775 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_171449_fad52bc3
2025-06-18 17:22:01,388 - main - INFO - Epinnox v6 starting up...
2025-06-18 17:22:01,401 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 17:22:01,401 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 17:22:01,402 - main - INFO - Performance monitoring initialized
2025-06-18 17:22:01,409 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 17:22:01,410 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 17:22:01,411 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 17:22:08,315 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 17:22:10,008 - websocket - INFO - Websocket connected
2025-06-18 17:22:13,853 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 17:22:13,854 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 17:22:13,854 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 17:22:13,855 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 17:22:13,859 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 17:22:15,904 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 17:22:15,905 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 17:22:15,905 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 17:22:15,908 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 17:22:15,908 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 17:22:15,917 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 17:22:15,918 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 17:22:15,918 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 17:22:15,926 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_172215_dfa62455
2025-06-18 17:22:15,929 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_172215_dfa62455
