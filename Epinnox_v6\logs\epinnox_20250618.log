2025-06-18 15:00:22,447 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:00:22,467 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:00:22,468 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:00:22,468 - main - INFO - Performance monitoring initialized
2025-06-18 15:00:22,478 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:00:22,478 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:00:22,480 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:00:23,724 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:00:24,869 - websocket - INFO - Websocket connected
2025-06-18 15:00:27,541 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:00:27,542 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:00:27,542 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:00:27,543 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:00:27,611 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:00:29,678 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:00:29,679 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:00:29,679 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:00:29,682 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:00:29,682 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:00:29,720 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:00:29,721 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:00:29,721 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:00:29,728 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_150029_c68bc157
2025-06-18 15:00:29,731 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_150029_c68bc157
2025-06-18 15:03:13,895 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:03:13,911 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:03:13,911 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:03:13,912 - main - INFO - Performance monitoring initialized
2025-06-18 15:03:13,919 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:03:13,920 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:03:13,921 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:03:15,141 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:03:16,164 - websocket - INFO - Websocket connected
2025-06-18 15:03:17,817 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:03:17,818 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:03:17,819 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:03:17,819 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:03:17,884 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:03:19,944 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:03:19,945 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:03:19,945 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:03:19,949 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:03:19,949 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:03:19,958 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:03:19,961 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:03:19,961 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:03:19,966 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_150319_842aa410
2025-06-18 15:03:19,971 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_150319_842aa410
2025-06-18 15:21:10,833 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:21:10,849 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:21:10,849 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:21:10,850 - main - INFO - Performance monitoring initialized
2025-06-18 15:21:10,858 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:21:10,858 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:21:10,859 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:21:15,371 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:21:16,422 - websocket - INFO - Websocket connected
2025-06-18 15:21:18,881 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:21:18,882 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:21:18,882 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:21:18,883 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:21:18,889 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:21:20,947 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:21:20,948 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:21:20,948 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:21:20,951 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:21:20,952 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:21:20,960 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:21:20,961 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:21:20,962 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:21:20,969 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152120_234ad650
2025-06-18 15:21:20,973 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152120_234ad650
2025-06-18 15:22:07,972 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:22:07,990 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:22:07,990 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:22:07,991 - main - INFO - Performance monitoring initialized
2025-06-18 15:22:08,001 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:22:08,002 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:22:08,003 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:22:12,709 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:22:13,605 - websocket - INFO - Websocket connected
2025-06-18 15:22:15,826 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:22:15,826 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:22:15,827 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:22:15,827 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:22:15,842 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:22:17,893 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:22:17,893 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:22:17,894 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:22:17,898 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:22:17,898 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:22:17,907 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:22:17,909 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:22:17,910 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:22:17,920 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152217_cc54e5d3
2025-06-18 15:22:17,923 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152217_cc54e5d3
2025-06-18 15:24:32,480 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:24:32,499 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:24:32,500 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:24:32,501 - main - INFO - Performance monitoring initialized
2025-06-18 15:24:32,508 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:24:32,509 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:24:32,510 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:24:37,316 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:24:38,194 - websocket - INFO - Websocket connected
2025-06-18 15:24:40,543 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:24:40,544 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:24:40,544 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:24:40,545 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:24:40,551 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:24:42,602 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:24:42,602 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:24:42,603 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:24:42,605 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:24:42,606 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:24:42,614 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:24:42,614 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:24:42,615 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:24:42,624 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152442_6e4b1cc3
2025-06-18 15:24:42,627 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152442_6e4b1cc3
2025-06-18 15:29:13,325 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:29:13,340 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:29:13,340 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:29:13,342 - main - INFO - Performance monitoring initialized
2025-06-18 15:29:13,349 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:29:13,349 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:29:13,350 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:29:19,499 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:29:20,370 - websocket - INFO - Websocket connected
2025-06-18 15:29:22,605 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:29:22,605 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:29:22,606 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:29:22,607 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:29:22,612 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:29:24,673 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:29:24,673 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:29:24,675 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:29:24,677 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:29:24,677 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:29:24,687 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:29:24,688 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:29:24,689 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:29:24,697 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_152924_5c416754
2025-06-18 15:29:24,700 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_152924_5c416754
2025-06-18 15:33:42,818 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:33:42,834 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:33:42,834 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:33:42,836 - main - INFO - Performance monitoring initialized
2025-06-18 15:33:42,844 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:33:42,844 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:33:42,845 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:33:47,060 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:33:48,490 - websocket - INFO - Websocket connected
2025-06-18 15:33:50,869 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:33:50,870 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:33:50,870 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:33:50,870 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:33:50,875 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:33:52,959 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:33:52,960 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:33:52,960 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:33:52,964 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:33:52,965 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:33:52,972 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:33:52,973 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:33:52,974 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:33:52,983 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_153352_b9816e98
2025-06-18 15:33:52,986 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_153352_b9816e98
2025-06-18 15:41:43,413 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:41:43,433 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:41:43,434 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:41:43,434 - main - INFO - Performance monitoring initialized
2025-06-18 15:41:43,443 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:41:43,444 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:41:43,445 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:41:47,705 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:41:48,851 - websocket - INFO - Websocket connected
2025-06-18 15:41:51,286 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:41:51,286 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:41:51,286 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:41:51,287 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:41:51,292 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:41:53,347 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:41:53,348 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:41:53,349 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:41:53,351 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:41:53,351 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:41:53,361 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:41:53,363 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:41:53,363 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:41:53,369 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_154153_259ebf1f
2025-06-18 15:41:53,372 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_154153_259ebf1f
2025-06-18 15:50:18,042 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:50:18,062 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:50:18,063 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:50:18,063 - main - INFO - Performance monitoring initialized
2025-06-18 15:50:18,072 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:50:18,072 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:50:18,073 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:50:22,297 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:50:23,197 - websocket - INFO - Websocket connected
2025-06-18 15:50:24,994 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:50:24,994 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:50:24,995 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:50:24,996 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:50:25,002 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:50:27,058 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:50:27,059 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:50:27,060 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:50:27,062 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:50:27,063 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:50:27,071 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:50:27,073 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:50:27,073 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:50:27,081 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155027_81b1dc90
2025-06-18 15:50:27,084 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155027_81b1dc90
2025-06-18 15:54:31,828 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:54:31,842 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:54:31,842 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:54:31,844 - main - INFO - Performance monitoring initialized
2025-06-18 15:54:31,852 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:54:31,852 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:54:31,853 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:54:36,486 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:54:37,531 - websocket - INFO - Websocket connected
2025-06-18 15:54:39,761 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:54:39,762 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:54:39,762 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:54:39,763 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:54:39,768 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:54:41,836 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:54:41,836 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:54:41,837 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:54:41,840 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:54:41,840 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:54:41,849 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:54:41,851 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:54:41,851 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:54:41,859 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155441_ba5d3153
2025-06-18 15:54:41,862 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155441_ba5d3153
2025-06-18 15:56:21,647 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:56:21,665 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:56:21,665 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:56:21,666 - main - INFO - Performance monitoring initialized
2025-06-18 15:56:21,674 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:56:21,675 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:56:21,676 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:56:25,651 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:56:26,622 - websocket - INFO - Websocket connected
2025-06-18 15:56:28,771 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:56:28,772 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:56:28,772 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:56:28,773 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:56:28,778 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:56:30,837 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:56:30,837 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:56:30,838 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:56:30,840 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:56:30,840 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:56:30,848 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:56:30,849 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:56:30,849 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:56:30,855 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155630_bf7572c4
2025-06-18 15:56:30,858 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155630_bf7572c4
2025-06-18 15:59:00,639 - main - INFO - Epinnox v6 starting up...
2025-06-18 15:59:00,654 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 15:59:00,655 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 15:59:00,657 - main - INFO - Performance monitoring initialized
2025-06-18 15:59:00,665 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 15:59:00,665 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 15:59:00,666 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 15:59:04,646 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 15:59:05,683 - websocket - INFO - Websocket connected
2025-06-18 15:59:08,550 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 15:59:08,551 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 15:59:08,551 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 15:59:08,552 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 15:59:08,556 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 15:59:10,628 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 15:59:10,628 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 15:59:10,630 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 15:59:10,632 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 15:59:10,633 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 15:59:10,641 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 15:59:10,642 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 15:59:10,643 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 15:59:10,652 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_155910_1d25eaae
2025-06-18 15:59:10,656 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_155910_1d25eaae
2025-06-18 16:01:35,516 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:01:35,538 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:01:35,539 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:01:35,539 - main - INFO - Performance monitoring initialized
2025-06-18 16:01:35,551 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:01:35,552 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:01:35,554 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:01:39,721 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:01:40,949 - websocket - INFO - Websocket connected
2025-06-18 16:01:43,594 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:01:43,595 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:01:43,596 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:01:43,596 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:01:43,602 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:01:45,665 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:01:45,665 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:01:45,666 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:01:45,668 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:01:45,668 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:01:45,676 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:01:45,677 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:01:45,678 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:01:45,686 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_160145_bee4b7f2
2025-06-18 16:01:45,688 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_160145_bee4b7f2
2025-06-18 16:04:23,042 - main - INFO - Epinnox v6 starting up...
2025-06-18 16:04:23,057 - core.performance_monitor - INFO - Performance monitoring started
2025-06-18 16:04:23,058 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-06-18 16:04:23,058 - main - INFO - Performance monitoring initialized
2025-06-18 16:04:23,066 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-06-18 16:04:23,066 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-06-18 16:04:23,068 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-06-18 16:04:28,056 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-06-18 16:04:29,675 - websocket - INFO - Websocket connected
2025-06-18 16:04:32,893 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-06-18 16:04:32,894 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-06-18 16:04:32,895 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-06-18 16:04:32,896 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-06-18 16:04:32,901 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-06-18 16:04:34,969 - llama.lmstudio_runner - INFO - Discovered 8 models: ['openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'phi-3.1-mini-128k-instruct', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-06-18 16:04:34,969 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-06-18 16:04:34,971 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-06-18 16:04:34,973 - core.signal_hierarchy - INFO - Intelligent Signal Hierarchy initialized
2025-06-18 16:04:34,973 - trading.signal_trading_engine - INFO - Signal Trading Engine initialized
2025-06-18 16:04:34,981 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-06-18 16:04:34,983 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-06-18 16:04:34,984 - storage.session_manager - INFO - Session Manager initialized
2025-06-18 16:04:34,989 - storage.database_manager - INFO - Created session: live_DOGEUSDTUSDT_20250618_160434_9a69d9ee
2025-06-18 16:04:34,993 - storage.session_manager - INFO - Started session: live_DOGEUSDTUSDT_20250618_160434_9a69d9ee
