"""
LMStudio API Integration
This module handles interaction with LMStudio's local API server with dynamic model switching.
"""
import requests
import logging
import json
import os
import yaml
from pathlib import Path
from typing import List, Dict, Optional
from PyQt5.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)


class LMStudioRunner(QObject):
    """
    Enhanced LMStudio runner with dynamic model switching and discovery.
    """

    # Signals for GUI integration
    model_changed = pyqtSignal(str)  # Emitted when model is switched
    models_discovered = pyqtSignal(list)  # Emitted when new models are found

    def __init__(self, api_url="http://localhost:1234/v1", model_name=None):
        """
        Initialize the LMStudio runner with dynamic model discovery.

        Args:
            api_url: Base URL for LMStudio API
            model_name: Name of the model to use (auto-detect if None)
        """
        super().__init__()
        self.api_url = api_url
        self.chat_url = f"{api_url}/chat/completions"
        self.available_models = []
        self.current_model = None
        self.model_details = {}

        # Load configuration
        self.config = self._load_config()

        # Discover and initialize models
        self._discover_models()

        # Set initial model with configuration preference
        if model_name and model_name in self.available_models:
            self.current_model = model_name
        elif self.available_models:
            # Try to use preferred model from config
            preferred_model = self.config.get('lmstudio', {}).get('preferred_model', 'phi')

            # Find models matching the preference
            preferred_models = [m for m in self.available_models if preferred_model.lower() in m.lower()]
            if preferred_models:
                self.current_model = preferred_models[0]
                logger.info(f"Selected preferred model from config: {self.current_model}")
            else:
                # Fallback to Phi model if available
                phi_models = [m for m in self.available_models if 'phi' in m.lower()]
                if phi_models:
                    self.current_model = phi_models[0]
                    logger.info(f"Selected fallback Phi model: {self.current_model}")
                else:
                    self.current_model = self.available_models[0]
                    logger.info(f"No preferred model found, using: {self.current_model}")
        else:
            logger.warning("No models available in LMStudio")

        if self.current_model:
            logger.info(f"Initialized LMStudio with model: {self.current_model}")
            self.model_changed.emit(self.current_model)

    def _load_config(self):
        """Load configuration from models_config.yaml"""
        try:
            config_path = Path(__file__).parent.parent / 'config' / 'models_config.yaml'
            if config_path.exists():
                with open(config_path, 'r') as f:
                    config = yaml.safe_load(f)
                    logger.info("Loaded LMStudio configuration from models_config.yaml")
                    return config
        except Exception as e:
            logger.warning(f"Could not load configuration: {e}")

        # Return default configuration
        return {
            'lmstudio': {
                'preferred_model': 'phi',
                'api_url': 'http://localhost:1234/v1'
            }
        }

    def _discover_models(self):
        """
        Discover available models from LMStudio API.
        """
        try:
            response = requests.get(f"{self.api_url}/models", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                self.available_models = []
                self.model_details = {}

                for model in models_data.get('data', []):
                    model_id = model.get('id', '')
                    if model_id:
                        self.available_models.append(model_id)
                        self.model_details[model_id] = {
                            'id': model_id,
                            'object': model.get('object', 'model'),
                            'created': model.get('created', 0),
                            'owned_by': model.get('owned_by', 'unknown')
                        }

                logger.info(f"Discovered {len(self.available_models)} models: {self.available_models}")
                self.models_discovered.emit(self.available_models)
                return True
            else:
                logger.error(f"Failed to discover models: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Error discovering models: {e}")
            return False

    def refresh_models(self):
        """
        Refresh the list of available models.

        Returns:
            bool: True if successful
        """
        return self._discover_models()

    def get_available_models(self) -> List[str]:
        """
        Get list of available model names.

        Returns:
            List of model names
        """
        return self.available_models.copy()

    def get_model_details(self, model_name: str) -> Optional[Dict]:
        """
        Get detailed information about a specific model.

        Args:
            model_name: Name of the model

        Returns:
            Dictionary with model details or None
        """
        return self.model_details.get(model_name)

    def switch_model(self, model_name: str) -> bool:
        """
        Switch to a different model.

        Args:
            model_name: Name of the model to switch to

        Returns:
            bool: True if successful
        """
        if model_name not in self.available_models:
            logger.error(f"Model {model_name} not available. Available models: {self.available_models}")
            return False

        old_model = self.current_model
        self.current_model = model_name
        logger.info(f"Switched model from {old_model} to {model_name}")
        self.model_changed.emit(model_name)
        return True

    def get_current_model(self) -> Optional[str]:
        """
        Get the currently selected model.

        Returns:
            Current model name or None
        """
        return self.current_model

    def run_inference(self, prompt, temperature=0.1, max_tokens=512):
        """
        Run inference with the currently selected LMStudio model.

        Args:
            prompt: The prompt to send to the model
            temperature: Temperature for sampling
            max_tokens: Maximum tokens to generate

        Returns:
            str: Model's response
        """
        if not self.current_model:
            logger.error("No model selected for inference")
            return "Error: No model selected"

        try:
            # Prepare the request
            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "model": self.current_model,
                "messages": [
                    {
                        "role": "system", 
                        "content": "You are a professional cryptocurrency trading analyst. Analyze the provided market data and provide trading recommendations. Always respond in this exact format:\n\nDECISION: [LONG/SHORT/WAIT]\nCONFIDENCE: [50-100]%\nTAKE_PROFIT: [percentage]%\nSTOP_LOSS: [percentage]%\nEXPLANATION: [Your detailed analysis]\n\nBe concise but thorough in your analysis."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }
            
            # Log the full request context for debugging
            logger.info(f"🤖 LMStudio Request - Model: {self.current_model}")
            logger.info(f"📝 System Prompt: {data['messages'][0]['content'][:200]}...")
            logger.info(f"💬 User Prompt: {data['messages'][1]['content'][:500]}...")
            logger.info(f"⚙️ Parameters - Temperature: {temperature}, Max Tokens: {max_tokens}")

            # Make the request
            response = requests.post(self.chat_url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                result = response.json()

                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']

                    # Log the response details
                    logger.info(f"✅ LMStudio Response - Length: {len(content)} chars")
                    logger.info(f"📄 Response Preview: {content[:300]}...")

                    # Log token usage if available
                    if 'usage' in result:
                        usage = result['usage']
                        logger.info(f"🔢 Token Usage - Prompt: {usage.get('prompt_tokens', 'N/A')}, "
                                  f"Completion: {usage.get('completion_tokens', 'N/A')}, "
                                  f"Total: {usage.get('total_tokens', 'N/A')}")

                    return content
                else:
                    logger.error("❌ No choices in LMStudio response")
                    logger.error(f"📄 Full response: {result}")
                    return "Error: No response from model"
            else:
                logger.error(f"❌ LMStudio API error: {response.status_code}")
                logger.error(f"📄 Error response: {response.text[:500]}...")
                return f"Error: LMStudio API returned {response.status_code}"
                
        except requests.exceptions.Timeout:
            logger.error("LMStudio request timed out")
            return "Error: Request timed out"
        except requests.exceptions.ConnectionError:
            logger.error("Could not connect to LMStudio")
            return "Error: Could not connect to LMStudio"
        except Exception as e:
            logger.error(f"Error in LMStudio inference: {e}")
            return f"Error: {str(e)}"
    
    def test_connection(self):
        """
        Test the connection to LMStudio.
        
        Returns:
            bool: True if connection is successful
        """
        try:
            response = requests.get(f"{self.api_url}/models", timeout=5)
            return response.status_code == 200
        except:
            return False
