"""
Live Data Manager for Epinnox v6
Coordinates WebSocket data feeds with chart updates and data storage
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from collections import deque
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

from .websocket_client import WebSocketClient


class LiveDataManager(QObject):
    """
    Manages live data feeds and coordinates with chart updates
    Handles data buffering, aggregation, and real-time updates
    """
    
    # Signals for UI updates
    chart_data_updated = pyqtSignal(str, dict)  # symbol, chart_data
    price_updated = pyqtSignal(str, float)  # symbol, price
    orderbook_updated = pyqtSignal(str, dict)  # symbol, orderbook
    connection_status_changed = pyqtSignal(bool)  # connected status
    
    def __init__(self, exchange_name="htx"):
        super().__init__()
        
        # Initialize WebSocket client
        self.ws_client = WebSocketClient(exchange_name)
        self.exchange_name = exchange_name
        
        # Data storage
        self.price_buffers = {}  # symbol -> deque of price points
        self.ohlcv_data = {}     # symbol -> timeframe -> OHLCV data
        self.current_candles = {}  # symbol -> timeframe -> current building candle
        
        # Configuration
        self.buffer_size = 1000  # Maximum price points to keep
        self.supported_timeframes = ["1m", "5m", "15m", "1h", "4h", "1d"]
        self.active_symbols = set()
        self.active_timeframes = set()
        
        # Timers for data aggregation
        self.aggregation_timers = {}
        
        # Connect WebSocket signals
        self._connect_websocket_signals()
        
        # Start data aggregation timers
        self._setup_aggregation_timers()
    
    def _connect_websocket_signals(self):
        """Connect WebSocket client signals to our handlers"""
        self.ws_client.price_update.connect(self._handle_price_update)
        self.ws_client.orderbook_update.connect(self._handle_orderbook_update)
        self.ws_client.trade_update.connect(self._handle_trade_update)
        self.ws_client.connection_status.connect(self._handle_connection_status)
        self.ws_client.error_occurred.connect(self._handle_error)
    
    def _setup_aggregation_timers(self):
        """Setup timers for OHLCV data aggregation"""
        # Timer for 1-minute candle aggregation
        self.minute_timer = QTimer()
        self.minute_timer.timeout.connect(self._aggregate_minute_candles)
        self.minute_timer.start(1000)  # Check every second
        
        # Timer for higher timeframe aggregation
        self.timeframe_timer = QTimer()
        self.timeframe_timer.timeout.connect(self._aggregate_higher_timeframes)
        self.timeframe_timer.start(5000)  # Check every 5 seconds
    
    def subscribe_symbol(self, symbol: str, timeframes: List[str] = None):
        """Subscribe to live data for a symbol"""
        try:
            if timeframes is None:
                timeframes = ["1m", "5m", "15m"]
            
            # Add to active symbols and timeframes
            self.active_symbols.add(symbol)
            self.active_timeframes.update(timeframes)
            
            # Initialize data structures
            if symbol not in self.price_buffers:
                self.price_buffers[symbol] = deque(maxlen=self.buffer_size)
            
            if symbol not in self.ohlcv_data:
                self.ohlcv_data[symbol] = {}
                self.current_candles[symbol] = {}
                
            for tf in timeframes:
                if tf not in self.ohlcv_data[symbol]:
                    self.ohlcv_data[symbol][tf] = deque(maxlen=500)  # Keep last 500 candles
                    self.current_candles[symbol][tf] = None
            
            # Fetch initial historical data to populate the chart
            self._fetch_initial_data(symbol, timeframes)

            # Subscribe to WebSocket feeds
            self.ws_client.subscribe_ticker(symbol)
            self.ws_client.subscribe_orderbook(symbol)
            self.ws_client.subscribe_trades(symbol)

            print(f"Subscribed to live data for {symbol} on timeframes: {timeframes}")

        except Exception as e:
            print(f"Error subscribing to {symbol}: {e}")

    def _fetch_initial_data(self, symbol: str, timeframes: List[str]):
        """Fetch initial historical data for chart display"""
        try:
            # Generate some recent mock data for immediate chart display
            import time
            import random

            current_time = int(time.time() * 1000)  # Current time in milliseconds
            base_price = 0.175 if "DOGE" in symbol else 2600  # Reasonable base prices

            # Generate last 50 candles for 1-minute timeframe
            mock_candles = []
            for i in range(50, 0, -1):  # Go backwards in time
                timestamp = current_time - (i * 60 * 1000)  # 1 minute intervals

                # Generate realistic OHLCV data
                open_price = base_price + random.uniform(-0.01, 0.01)
                high_price = open_price + random.uniform(0, 0.005)
                low_price = open_price - random.uniform(0, 0.005)
                close_price = open_price + random.uniform(-0.005, 0.005)
                volume = random.uniform(1000, 10000)

                candle = [timestamp, open_price, high_price, low_price, close_price, volume]
                mock_candles.append(candle)
                base_price = close_price  # Use close as next open

            # Store in 1m data
            if "1m" in timeframes:
                self.ohlcv_data[symbol]["1m"] = deque(mock_candles, maxlen=500)

                # Emit initial chart data
                chart_data = {
                    "timeframe": "1m",
                    "ohlcv": list(self.ohlcv_data[symbol]["1m"])
                }
                self.chart_data_updated.emit(symbol, chart_data)
                print(f"📊 Loaded {len(mock_candles)} initial candles for {symbol}")

        except Exception as e:
            print(f"Error fetching initial data for {symbol}: {e}")
    
    def unsubscribe_symbol(self, symbol: str):
        """Unsubscribe from live data for a symbol"""
        try:
            self.active_symbols.discard(symbol)
            
            # Clean up data structures
            if symbol in self.price_buffers:
                del self.price_buffers[symbol]
            if symbol in self.ohlcv_data:
                del self.ohlcv_data[symbol]
            if symbol in self.current_candles:
                del self.current_candles[symbol]
                
            print(f"Unsubscribed from live data for {symbol}")
            
        except Exception as e:
            print(f"Error unsubscribing from {symbol}: {e}")
    
    def get_chart_data(self, symbol: str, timeframe: str, limit: int = 100) -> List[List]:
        """Get chart data in OHLCV format for a symbol and timeframe"""
        try:
            if symbol not in self.ohlcv_data or timeframe not in self.ohlcv_data[symbol]:
                return []
            
            candles = list(self.ohlcv_data[symbol][timeframe])
            
            # Return last 'limit' candles
            return candles[-limit:] if len(candles) > limit else candles
            
        except Exception as e:
            print(f"Error getting chart data for {symbol} {timeframe}: {e}")
            return []
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get latest price for a symbol"""
        try:
            if symbol in self.price_buffers and self.price_buffers[symbol]:
                return self.price_buffers[symbol][-1]["price"]
            return None
        except Exception as e:
            print(f"Error getting latest price for {symbol}: {e}")
            return None
    
    def get_latest_orderbook(self, symbol: str) -> Optional[dict]:
        """Get latest order book for a symbol"""
        return self.ws_client.get_latest_orderbook(symbol)
    
    def _handle_price_update(self, symbol: str, price_data: dict):
        """Handle incoming price updates from WebSocket"""
        try:
            # Add to price buffer
            if symbol in self.price_buffers:
                self.price_buffers[symbol].append(price_data)
                
                # Emit price update signal
                self.price_updated.emit(symbol, price_data["price"])
                
                # Update current candles
                self._update_current_candles(symbol, price_data)
                
        except Exception as e:
            print(f"Error handling price update for {symbol}: {e}")
    
    def _handle_orderbook_update(self, symbol: str, orderbook_data: dict):
        """Handle incoming order book updates from WebSocket"""
        try:
            # Emit orderbook update signal
            self.orderbook_updated.emit(symbol, orderbook_data)
        except Exception as e:
            print(f"Error handling orderbook update for {symbol}: {e}")
    
    def _handle_trade_update(self, symbol: str, trade_data: dict):
        """Handle incoming trade updates from WebSocket"""
        try:
            # Process trade data for volume calculations
            pass
        except Exception as e:
            print(f"Error handling trade update for {symbol}: {e}")
    
    def _handle_connection_status(self, connected: bool):
        """Handle WebSocket connection status changes"""
        self.connection_status_changed.emit(connected)
        status = "Connected" if connected else "Disconnected"
        print(f"WebSocket {status}")
    
    def _handle_error(self, error_msg: str):
        """Handle WebSocket errors"""
        print(f"WebSocket Error: {error_msg}")
    
    def _update_current_candles(self, symbol: str, price_data: dict):
        """Update current building candles with new price data"""
        try:
            if symbol not in self.current_candles:
                return
            
            timestamp = price_data["timestamp"]
            price = price_data["price"]
            volume = price_data.get("volume", 0)
            
            for timeframe in self.current_candles[symbol]:
                # Get timeframe duration in seconds
                tf_seconds = self._get_timeframe_seconds(timeframe)
                
                # Calculate candle start time
                candle_start = int(timestamp // tf_seconds) * tf_seconds
                
                # Get or create current candle
                current_candle = self.current_candles[symbol][timeframe]
                
                if current_candle is None or current_candle[0] != candle_start * 1000:
                    # Start new candle
                    self.current_candles[symbol][timeframe] = [
                        candle_start * 1000,  # timestamp in ms
                        price,  # open
                        price,  # high
                        price,  # low
                        price,  # close
                        volume  # volume
                    ]
                else:
                    # Update existing candle
                    current_candle[2] = max(current_candle[2], price)  # high
                    current_candle[3] = min(current_candle[3], price)  # low
                    current_candle[4] = price  # close
                    current_candle[5] += volume  # volume
                    
        except Exception as e:
            print(f"Error updating current candles for {symbol}: {e}")
    
    def _aggregate_minute_candles(self):
        """Aggregate completed minute candles"""
        try:
            current_time = time.time()
            
            for symbol in self.active_symbols:
                if symbol not in self.current_candles:
                    continue
                    
                # Check 1-minute candles
                if "1m" in self.current_candles[symbol]:
                    current_candle = self.current_candles[symbol]["1m"]
                    
                    if current_candle is not None:
                        candle_start = current_candle[0] / 1000
                        
                        # If candle is complete (more than 1 minute old)
                        if current_time - candle_start >= 60:
                            # Add to OHLCV data
                            self.ohlcv_data[symbol]["1m"].append(current_candle.copy())
                            
                            # Emit chart update
                            chart_data = {
                                "timeframe": "1m",
                                "ohlcv": list(self.ohlcv_data[symbol]["1m"])
                            }
                            self.chart_data_updated.emit(symbol, chart_data)
                            
                            # Reset current candle
                            self.current_candles[symbol]["1m"] = None
                            
        except Exception as e:
            print(f"Error aggregating minute candles: {e}")
    
    def _aggregate_higher_timeframes(self):
        """Aggregate higher timeframe candles from 1-minute data"""
        try:
            for symbol in self.active_symbols:
                if symbol not in self.ohlcv_data or "1m" not in self.ohlcv_data[symbol]:
                    continue
                
                minute_candles = list(self.ohlcv_data[symbol]["1m"])
                if not minute_candles:
                    continue
                
                # Aggregate for each timeframe
                for timeframe in ["5m", "15m", "1h", "4h"]:
                    if timeframe in self.active_timeframes:
                        self._aggregate_timeframe(symbol, timeframe, minute_candles)
                        
        except Exception as e:
            print(f"Error aggregating higher timeframes: {e}")
    
    def _aggregate_timeframe(self, symbol: str, timeframe: str, minute_candles: List):
        """Aggregate specific timeframe from minute candles"""
        try:
            tf_minutes = self._get_timeframe_minutes(timeframe)
            
            # Group minute candles by timeframe periods
            aggregated_candles = []
            
            for i in range(0, len(minute_candles), tf_minutes):
                period_candles = minute_candles[i:i + tf_minutes]
                if not period_candles:
                    continue
                
                # Create aggregated candle
                timestamp = period_candles[0][0]
                open_price = period_candles[0][1]
                high_price = max(candle[2] for candle in period_candles)
                low_price = min(candle[3] for candle in period_candles)
                close_price = period_candles[-1][4]
                volume = sum(candle[5] for candle in period_candles)
                
                aggregated_candle = [timestamp, open_price, high_price, low_price, close_price, volume]
                aggregated_candles.append(aggregated_candle)
            
            # Update OHLCV data
            if aggregated_candles:
                self.ohlcv_data[symbol][timeframe] = deque(aggregated_candles, maxlen=500)
                
                # Emit chart update
                chart_data = {
                    "timeframe": timeframe,
                    "ohlcv": aggregated_candles
                }
                self.chart_data_updated.emit(symbol, chart_data)
                
        except Exception as e:
            print(f"Error aggregating {timeframe} for {symbol}: {e}")
    
    def _get_timeframe_seconds(self, timeframe: str) -> int:
        """Convert timeframe string to seconds"""
        multipliers = {"m": 60, "h": 3600, "d": 86400}
        if timeframe[-1] in multipliers:
            return int(timeframe[:-1]) * multipliers[timeframe[-1]]
        return 60  # Default to 1 minute
    
    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes"""
        if timeframe.endswith("m"):
            return int(timeframe[:-1])
        elif timeframe.endswith("h"):
            return int(timeframe[:-1]) * 60
        elif timeframe.endswith("d"):
            return int(timeframe[:-1]) * 1440
        return 1  # Default to 1 minute
    
    def connect(self):
        """Connect to live data feeds"""
        return self.ws_client.connect()
    
    def disconnect(self):
        """Disconnect from live data feeds"""
        self.ws_client.disconnect()
        
        # Stop timers
        if hasattr(self, 'minute_timer'):
            self.minute_timer.stop()
        if hasattr(self, 'timeframe_timer'):
            self.timeframe_timer.stop()
