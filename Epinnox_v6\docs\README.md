# Epinnox v6 Documentation

Welcome to the comprehensive documentation for Epinnox v6 - AI-Powered Trading System.

## 📚 Documentation Structure

### 🚀 Getting Started
- [Main README](../README.md) - Project overview and quick start
- [GUI Setup Guide](guides/gui-setup.md) - Setting up the graphical interface
- [GUI User Guide](guides/gui-guide.md) - Complete GUI usage documentation

### ✨ Features
- [ML Prediction Accuracy Tracking](features/PREDICTION_ACCURACY_FEATURE.md) - Real-time ML model performance evaluation
- [UI Reorganization](features/ui-reorganization.md) - Modern interface improvements

### 🔧 API Documentation
- [Trading API](api/trading-api.md) - Trading system API reference

### 📖 Guides
- [GitHub Publication Guide](guides/github-publication.md) - Repository publication guidelines

## 🎯 Quick Navigation

### For New Users
1. Start with the [Main README](../README.md) for project overview
2. Follow the [GUI Setup Guide](guides/gui-setup.md) to get started
3. Read the [GUI User Guide](guides/gui-guide.md) for detailed usage

### For Developers
1. Check the [Trading API](api/trading-api.md) for integration details
2. Review [ML Prediction Accuracy](features/PREDICTION_ACCURACY_FEATURE.md) for the latest features
3. See [UI Reorganization](features/ui-reorganization.md) for interface architecture

### For Contributors
1. Read the [GitHub Publication Guide](guides/github-publication.md)
2. Review the feature documentation for implementation details

## 🔍 Key Features Documented

### 🆕 Latest Features
- **ML Prediction Accuracy Tracking**: Real-time evaluation of machine learning model performance
- **Professional GUI**: Matrix-themed interface with dockable panels
- **Live Data Integration**: HTX exchange WebSocket feeds
- **Real Trading Interface**: Production-ready order execution

### 🤖 AI & ML Components
- Multi-model ML predictions (SVM, Random Forest, LSTM)
- LLM integration (LLaMA, Phi, GPT-4)
- Real-time accuracy tracking and validation
- Confidence-based decision making

### 📊 Trading Features
- Multi-timeframe analysis (1m, 5m, 15m)
- Dynamic risk management
- Position sizing and leverage control
- Real-time market data processing

## 🛠️ Technical Documentation

### Architecture
- **Core Trading System**: `core/` - Main trading algorithms
- **GUI Components**: `gui/` - PyQt5 interface modules
- **Data Management**: `data/` - Live data feeds and storage
- **ML Models**: `ml/` - Machine learning components
- **Trading Execution**: `trading/` - Order execution and management

### Configuration
- **Trading Parameters**: `config/trading_config.yaml`
- **Model Configuration**: `config/models_config.yaml`
- **API Credentials**: `config/credentials.yaml`

## 📈 Performance & Monitoring

### Real-time Metrics
- ML model accuracy percentages
- Live trading performance
- Risk factor analysis
- System health monitoring

### Data Sources
- HTX (Huobi) exchange integration
- Real-time WebSocket feeds
- Historical data caching
- Order book analysis

## 🔐 Security & Configuration

### API Security
- Secure credential management
- Environment-based configuration
- Production vs sandbox modes

### Risk Management
- Dynamic stop-loss calculation
- Position sizing controls
- Market regime detection
- Confidence-based scaling

## 🆘 Support & Troubleshooting

### Common Issues
- API authentication problems
- WebSocket connection issues
- GUI display problems
- Model loading errors

### Getting Help
- Check the relevant guide in `guides/`
- Review API documentation in `api/`
- See feature-specific docs in `features/`

## 🔄 Updates & Changelog

### Latest Version Features
- ✅ ML Prediction Accuracy Tracking
- ✅ Enhanced GUI with Matrix theme
- ✅ Real-time WebSocket integration
- ✅ Production trading interface

### Upcoming Features
- Historical accuracy charts
- Advanced backtesting engine
- Multi-exchange support
- Enhanced risk analytics

---

**For the most up-to-date information, always refer to the [Main README](../README.md) and individual feature documentation.**
