"""
Database Manager for Epinnox v6
SQLite database management for trades, sessions, and performance data
"""

import sqlite3
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages SQLite database for storing trading data, sessions, and performance metrics
    """
    
    def __init__(self, db_path: str = "data/epinnox_trading.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self.init_database()
        logger.info(f"Database manager initialized: {self.db_path}")
    
    def init_database(self):
        """Initialize database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Trading sessions table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trading_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT UNIQUE NOT NULL,
                        start_time TIMESTAMP NOT NULL,
                        end_time TIMESTAMP,
                        mode TEXT NOT NULL,  -- 'live', 'demo', 'backtest'
                        symbol TEXT NOT NULL,
                        initial_balance REAL NOT NULL,
                        final_balance REAL,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        total_pnl REAL DEFAULT 0,
                        max_drawdown REAL DEFAULT 0,
                        configuration TEXT,  -- JSON configuration
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Trades table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trades (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        trade_id TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,  -- 'buy', 'sell'
                        type TEXT NOT NULL,  -- 'market', 'limit'
                        entry_time TIMESTAMP NOT NULL,
                        exit_time TIMESTAMP,
                        entry_price REAL NOT NULL,
                        exit_price REAL,
                        quantity REAL NOT NULL,
                        leverage INTEGER NOT NULL,
                        pnl REAL DEFAULT 0,
                        pnl_percent REAL DEFAULT 0,
                        fees REAL DEFAULT 0,
                        duration_minutes INTEGER,
                        signal_source TEXT,
                        signal_confidence REAL,
                        signal_reasoning TEXT,
                        metadata TEXT,  -- JSON metadata
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
                    )
                """)
                
                # Signals table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS signals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        symbol TEXT NOT NULL,
                        source TEXT NOT NULL,  -- 'ml_ensemble', 'llm_analysis', etc.
                        decision TEXT NOT NULL,  -- 'LONG', 'SHORT', 'WAIT'
                        confidence REAL NOT NULL,
                        weight REAL NOT NULL,
                        reasoning TEXT,
                        executed BOOLEAN DEFAULT FALSE,
                        trade_id TEXT,
                        metadata TEXT,  -- JSON metadata
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
                    )
                """)
                
                # Performance metrics table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        balance REAL NOT NULL,
                        equity REAL NOT NULL,
                        unrealized_pnl REAL DEFAULT 0,
                        realized_pnl REAL DEFAULT 0,
                        drawdown REAL DEFAULT 0,
                        open_positions INTEGER DEFAULT 0,
                        daily_trades INTEGER DEFAULT 0,
                        win_rate REAL DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (session_id)
                    )
                """)
                
                # Market data table (for backtesting)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS market_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        timestamp TIMESTAMP NOT NULL,
                        timeframe TEXT NOT NULL,
                        open_price REAL NOT NULL,
                        high_price REAL NOT NULL,
                        low_price REAL NOT NULL,
                        close_price REAL NOT NULL,
                        volume REAL NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(symbol, timestamp, timeframe)
                    )
                """)
                
                # Create indexes for better performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_session ON trades(session_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_trades_time ON trades(entry_time)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_session ON signals(session_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_signals_time ON signals(timestamp)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_performance_session ON performance_metrics(session_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data(symbol, timestamp)")
                
                conn.commit()
                logger.info("Database tables initialized successfully")
                
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    # Session management
    def create_session(self, session_id: str, mode: str, symbol: str, initial_balance: float, 
                      configuration: Dict = None) -> bool:
        """Create a new trading session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO trading_sessions 
                    (session_id, start_time, mode, symbol, initial_balance, configuration)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    datetime.now(),
                    mode,
                    symbol,
                    initial_balance,
                    json.dumps(configuration) if configuration else None
                ))
                conn.commit()
                logger.info(f"Created session: {session_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return False
    
    def end_session(self, session_id: str, final_balance: float, total_trades: int, 
                   winning_trades: int, total_pnl: float, max_drawdown: float) -> bool:
        """End a trading session with final metrics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE trading_sessions 
                    SET end_time = ?, final_balance = ?, total_trades = ?, 
                        winning_trades = ?, total_pnl = ?, max_drawdown = ?
                    WHERE session_id = ?
                """, (
                    datetime.now(),
                    final_balance,
                    total_trades,
                    winning_trades,
                    total_pnl,
                    max_drawdown,
                    session_id
                ))
                conn.commit()
                logger.info(f"Ended session: {session_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error ending session: {e}")
            return False
    
    def get_sessions(self, limit: int = 100) -> List[Dict]:
        """Get recent trading sessions"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM trading_sessions 
                    ORDER BY start_time DESC 
                    LIMIT ?
                """, (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                sessions = []
                
                for row in cursor.fetchall():
                    session = dict(zip(columns, row))
                    if session['configuration']:
                        session['configuration'] = json.loads(session['configuration'])
                    sessions.append(session)
                
                return sessions
                
        except Exception as e:
            logger.error(f"Error getting sessions: {e}")
            return []
    
    # Trade management
    def record_trade(self, session_id: str, trade_data: Dict) -> bool:
        """Record a trade in the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO trades 
                    (session_id, trade_id, symbol, side, type, entry_time, exit_time,
                     entry_price, exit_price, quantity, leverage, pnl, pnl_percent, fees,
                     duration_minutes, signal_source, signal_confidence, signal_reasoning, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    trade_data.get('trade_id', ''),
                    trade_data.get('symbol', ''),
                    trade_data.get('side', ''),
                    trade_data.get('type', ''),
                    trade_data.get('entry_time'),
                    trade_data.get('exit_time'),
                    trade_data.get('entry_price', 0),
                    trade_data.get('exit_price'),
                    trade_data.get('quantity', 0),
                    trade_data.get('leverage', 1),
                    trade_data.get('pnl', 0),
                    trade_data.get('pnl_percent', 0),
                    trade_data.get('fees', 0),
                    trade_data.get('duration_minutes'),
                    trade_data.get('signal_source'),
                    trade_data.get('signal_confidence'),
                    trade_data.get('signal_reasoning'),
                    json.dumps(trade_data.get('metadata', {}))
                ))
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
            return False
    
    def get_trades(self, session_id: str = None, limit: int = 1000) -> List[Dict]:
        """Get trades for a session or all trades"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if session_id:
                    cursor.execute("""
                        SELECT * FROM trades 
                        WHERE session_id = ? 
                        ORDER BY entry_time DESC 
                        LIMIT ?
                    """, (session_id, limit))
                else:
                    cursor.execute("""
                        SELECT * FROM trades 
                        ORDER BY entry_time DESC 
                        LIMIT ?
                    """, (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                trades = []
                
                for row in cursor.fetchall():
                    trade = dict(zip(columns, row))
                    if trade['metadata']:
                        trade['metadata'] = json.loads(trade['metadata'])
                    trades.append(trade)
                
                return trades
                
        except Exception as e:
            logger.error(f"Error getting trades: {e}")
            return []
    
    # Signal management
    def record_signal(self, session_id: str, signal_data: Dict) -> bool:
        """Record a signal in the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO signals 
                    (session_id, timestamp, symbol, source, decision, confidence, weight,
                     reasoning, executed, trade_id, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    signal_data.get('timestamp', datetime.now()),
                    signal_data.get('symbol', ''),
                    signal_data.get('source', ''),
                    signal_data.get('decision', ''),
                    signal_data.get('confidence', 0),
                    signal_data.get('weight', 0),
                    signal_data.get('reasoning', ''),
                    signal_data.get('executed', False),
                    signal_data.get('trade_id'),
                    json.dumps(signal_data.get('metadata', {}))
                ))
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error recording signal: {e}")
            return False
    
    def get_signals(self, session_id: str = None, limit: int = 1000) -> List[Dict]:
        """Get signals for a session or all signals"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if session_id:
                    cursor.execute("""
                        SELECT * FROM signals 
                        WHERE session_id = ? 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    """, (session_id, limit))
                else:
                    cursor.execute("""
                        SELECT * FROM signals 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    """, (limit,))
                
                columns = [desc[0] for desc in cursor.description]
                signals = []
                
                for row in cursor.fetchall():
                    signal = dict(zip(columns, row))
                    if signal['metadata']:
                        signal['metadata'] = json.loads(signal['metadata'])
                    signals.append(signal)
                
                return signals
                
        except Exception as e:
            logger.error(f"Error getting signals: {e}")
            return []
    
    # Performance tracking
    def record_performance_snapshot(self, session_id: str, metrics: Dict) -> bool:
        """Record a performance snapshot"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO performance_metrics 
                    (session_id, timestamp, balance, equity, unrealized_pnl, realized_pnl,
                     drawdown, open_positions, daily_trades, win_rate)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    metrics.get('timestamp', datetime.now()),
                    metrics.get('balance', 0),
                    metrics.get('equity', 0),
                    metrics.get('unrealized_pnl', 0),
                    metrics.get('realized_pnl', 0),
                    metrics.get('drawdown', 0),
                    metrics.get('open_positions', 0),
                    metrics.get('daily_trades', 0),
                    metrics.get('win_rate', 0)
                ))
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error recording performance snapshot: {e}")
            return False
    
    def get_performance_history(self, session_id: str) -> List[Dict]:
        """Get performance history for a session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM performance_metrics 
                    WHERE session_id = ? 
                    ORDER BY timestamp ASC
                """, (session_id,))
                
                columns = [desc[0] for desc in cursor.description]
                metrics = []
                
                for row in cursor.fetchall():
                    metric = dict(zip(columns, row))
                    metrics.append(metric)
                
                return metrics
                
        except Exception as e:
            logger.error(f"Error getting performance history: {e}")
            return []
    
    # Market data management
    def store_market_data(self, symbol: str, timeframe: str, data: List[Dict]) -> bool:
        """Store market data for backtesting"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for candle in data:
                    cursor.execute("""
                        INSERT OR REPLACE INTO market_data 
                        (symbol, timestamp, timeframe, open_price, high_price, low_price, close_price, volume)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        symbol,
                        candle['timestamp'],
                        timeframe,
                        candle['open'],
                        candle['high'],
                        candle['low'],
                        candle['close'],
                        candle['volume']
                    ))
                
                conn.commit()
                logger.info(f"Stored {len(data)} market data points for {symbol} {timeframe}")
                return True
                
        except Exception as e:
            logger.error(f"Error storing market data: {e}")
            return False
    
    def get_market_data(self, symbol: str, timeframe: str, start_time: datetime = None, 
                       end_time: datetime = None) -> pd.DataFrame:
        """Get market data for backtesting"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = """
                    SELECT timestamp, open_price, high_price, low_price, close_price, volume
                    FROM market_data 
                    WHERE symbol = ? AND timeframe = ?
                """
                params = [symbol, timeframe]
                
                if start_time:
                    query += " AND timestamp >= ?"
                    params.append(start_time)
                
                if end_time:
                    query += " AND timestamp <= ?"
                    params.append(end_time)
                
                query += " ORDER BY timestamp ASC"
                
                df = pd.read_sql_query(query, conn, params=params)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.rename(columns={
                    'open_price': 'open',
                    'high_price': 'high', 
                    'low_price': 'low',
                    'close_price': 'close'
                }, inplace=True)
                
                return df
                
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return pd.DataFrame()
    
    # Analytics and reporting
    def get_session_summary(self, session_id: str) -> Dict:
        """Get comprehensive session summary"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get session info
                cursor.execute("SELECT * FROM trading_sessions WHERE session_id = ?", (session_id,))
                session_row = cursor.fetchone()
                if not session_row:
                    return {}
                
                columns = [desc[0] for desc in cursor.description]
                session_info = dict(zip(columns, session_row))
                
                # Get trade statistics
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_trades,
                        SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                        SUM(pnl) as total_pnl,
                        AVG(pnl) as avg_pnl,
                        MAX(pnl) as best_trade,
                        MIN(pnl) as worst_trade,
                        AVG(duration_minutes) as avg_duration
                    FROM trades 
                    WHERE session_id = ?
                """, (session_id,))
                
                trade_stats = cursor.fetchone()
                
                # Get signal statistics
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_signals,
                        SUM(CASE WHEN executed = 1 THEN 1 ELSE 0 END) as executed_signals,
                        AVG(confidence) as avg_confidence
                    FROM signals 
                    WHERE session_id = ?
                """, (session_id,))
                
                signal_stats = cursor.fetchone()
                
                # Combine results
                summary = {
                    'session_info': session_info,
                    'trade_stats': {
                        'total_trades': trade_stats[0] or 0,
                        'winning_trades': trade_stats[1] or 0,
                        'losing_trades': (trade_stats[0] or 0) - (trade_stats[1] or 0),
                        'win_rate': (trade_stats[1] / trade_stats[0] * 100) if trade_stats[0] else 0,
                        'total_pnl': trade_stats[2] or 0,
                        'avg_pnl': trade_stats[3] or 0,
                        'best_trade': trade_stats[4] or 0,
                        'worst_trade': trade_stats[5] or 0,
                        'avg_duration_minutes': trade_stats[6] or 0
                    },
                    'signal_stats': {
                        'total_signals': signal_stats[0] or 0,
                        'executed_signals': signal_stats[1] or 0,
                        'execution_rate': (signal_stats[1] / signal_stats[0] * 100) if signal_stats[0] else 0,
                        'avg_confidence': signal_stats[2] or 0
                    }
                }
                
                return summary
                
        except Exception as e:
            logger.error(f"Error getting session summary: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old data to manage database size"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete old sessions and related data
                cursor.execute("DELETE FROM trading_sessions WHERE start_time < ?", (cutoff_date,))
                cursor.execute("DELETE FROM trades WHERE created_at < ?", (cutoff_date,))
                cursor.execute("DELETE FROM signals WHERE created_at < ?", (cutoff_date,))
                cursor.execute("DELETE FROM performance_metrics WHERE created_at < ?", (cutoff_date,))
                
                # Vacuum database to reclaim space
                cursor.execute("VACUUM")
                
                conn.commit()
                logger.info(f"Cleaned up data older than {days_to_keep} days")
                
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
    
    def export_session_data(self, session_id: str, export_path: str) -> bool:
        """Export session data to JSON file"""
        try:
            summary = self.get_session_summary(session_id)
            trades = self.get_trades(session_id)
            signals = self.get_signals(session_id)
            performance = self.get_performance_history(session_id)
            
            export_data = {
                'session_summary': summary,
                'trades': trades,
                'signals': signals,
                'performance_history': performance,
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            logger.info(f"Exported session data to {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting session data: {e}")
            return False
