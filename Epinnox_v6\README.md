# Epinnox v6 - AI-Powered Trading System

🚀 **Professional-grade cryptocurrency trading system combining numerical analysis with AI/LLM decision making**

## 🎯 NEW: ML Prediction Accuracy Tracking

**Latest Feature**: Real-time evaluation of ML model performance with live accuracy tracking and color-coded indicators.

### ✨ Key Highlights
- **Real-time Accuracy Tracking**: Monitor SVM, Random Forest, and LSTM prediction accuracy in real-time
- **Color-coded Performance**: Green (≥70%), Yellow (50-69%), Red (<50%) accuracy indicators
- **Live WebSocket Integration**: HTX exchange real-time data feeds for accurate evaluation
- **5-minute Evaluation Window**: Compare predictions with actual price movements
- **Historical Performance**: Track model accuracy trends over time

## 🌟 Features

### 🤖 AI-Powered Analysis
- **Multi-Model Support**: LLaMA, Phi, GPT-4, and local LLM integration
- **LMStudio Integration**: Local AI model inference
- **Smart Decision Making**: AI-driven trading decisions with confidence scoring
- **Market Regime Detection**: Automatic market condition analysis
- **🆕 ML Prediction Accuracy Tracking**: Real-time evaluation of ML model performance with live accuracy percentages

### 📊 Advanced Technical Analysis
- **Multi-Timeframe Analysis**: 1m, 5m, 15m trend confirmation
- **Signal Scoring System**: MACD, Volume, Price Action, Order Book analysis
- **Adaptive Risk Management**: Dynamic stop-loss and take-profit calculation
- **Market Regime Detection**: Low/High volatility and trend strength analysis

### 💹 Real-Time Trading
- **Exchange Integration**: HTX (Huobi) spot and futures markets via ccxt
- **Live Data Feeds**: Real-time OHLCV, order book, and trades data
- **Position Management**: Automated position scaling and risk control
- **Continuous Operation**: 24/7 trading with configurable intervals

### 🎨 Professional GUI (Matrix Theme)
- **Real-Time Dashboard**: Live market data and trading decisions
- **Interactive Charts**: OHLCV candlestick charts with technical indicators
- **Signal Visualization**: Individual signal scores and confidence levels
- **Risk Management Panel**: Real-time risk factor analysis
- **Matrix Theme**: Cyberpunk-style interface with authentic terminal feel

## 🚀 Quick Start

### Prerequisites
```bash
pip install -r requirements.txt
```

### Terminal Trading
```bash
# Single analysis
python main.py --symbol DOGE/USDT

# Continuous trading (recommended)
python main.py --symbol DOGE/USDT --continuous --delay 60

# Live trades data
python main.py --symbol DOGE/USDT --continuous --live
```

### GUI Interface
```bash
python launch_gui.py
```

## 📁 Project Structure

```
Epinnox_v6/
├── main.py                 # Main trading system entry point
├── launch_gui.py           # GUI application launcher
├── epinnox.py             # Alternative GUI entry point
├── startup.py             # System initialization
├── requirements.txt       # Python dependencies
├── setup.py              # Package setup
│
├── core/                  # Core trading algorithms
│   ├── adaptive_risk.py   # Risk management
│   ├── market_regime.py   # Market condition detection
│   ├── multi_timeframe.py # Multi-timeframe analysis
│   ├── signal_scoring.py  # Signal aggregation
│   └── features.py        # Technical indicators
│
├── data/                  # Data management
│   ├── exchange.py        # Exchange connectivity (ccxt)
│   └── shared_data_manager.py
│
├── gui/                   # PyQt5 GUI components
│   ├── main_window.py     # Main GUI window
│   ├── components/        # UI components
│   └── data_manager.py    # GUI data handling
│
├── llama/                 # AI/LLM integration
│   ├── runner.py          # Main LLM interface
│   ├── lmstudio_runner.py # LMStudio integration
│   ├── chatgpt_runner.py  # OpenAI GPT integration
│   └── mock_runner.py     # Development/testing
│
├── trading/               # Trading execution
│   ├── live_trader.py     # Live trading
│   ├── simulation_trader.py # Paper trading
│   └── trading_manager.py
│
├── config/                # Configuration files
│   ├── trading_config.yaml
│   ├── models_config.yaml
│   └── credentials.yaml
│
├── tests/                 # Test suite
├── logs/                  # System logs
├── cache/                 # Data cache
└── assets/               # GUI assets
```

## ⚙️ Configuration

### Trading Parameters
Edit `config/trading_config.yaml`:
```yaml
symbol: "DOGE/USDT"
leverage: 75
balance: 500
timeframes: ["1m", "5m", "15m"]
```

### AI Models
Configure in `config/models_config.yaml`:
```yaml
model_type: "lmstudio"  # or "openai", "local"
model_name: "phi-3.1-mini-128k-instruct"
```

### Exchange Credentials
Set up `config/credentials.yaml`:
```yaml
htx:
  api_key: "your_api_key"
  secret: "your_secret"
  sandbox: true
```

## 🎯 Trading Modes

### 1. Analysis Mode (Single Run)
- Analyzes current market conditions
- Provides trading recommendation
- Exits after analysis

### 2. Continuous Mode (Recommended)
- Runs indefinitely with configurable delays
- Monitors market conditions continuously
- Adapts to changing market regimes

### 3. Live Trading Mode
- Executes actual trades (when configured)
- Real-time position management
- Automated risk control

## 🎨 GUI Features

### Matrix Theme Interface
- **Authentic Terminal Feel**: Monospace fonts and green-on-black color scheme
- **Real-Time Updates**: Live market data and trading decisions
- **Interactive Charts**: Zoom, pan, and analyze price movements
- **Modular Panels**: Drag and drop interface customization

### Key Panels
- **Live Chart**: OHLCV candlestick chart with volume
- **Market Data**: Order book and recent trades
- **Signal Scoring**: Individual indicator analysis
- **AI Analysis**: LLM trading decisions and reasoning
- **🆕 ML Models Status**: Real-time ML prediction accuracy tracking with color-coded performance indicators
- **Risk Management**: Real-time risk factor breakdown
- **System Status**: Performance metrics and system health

## 🔧 Development

### Running Tests
```bash
cd tests/
python -m pytest
```

### Adding New Indicators
1. Add indicator logic to `core/features.py`
2. Update signal scoring in `core/signal_scoring.py`
3. Add GUI visualization in `gui/components/`

### Custom AI Models
1. Create new runner in `llama/`
2. Update factory in `llama/factory.py`
3. Configure in `config/models_config.yaml`

## 📈 Performance

- **Real-Time Analysis**: Sub-second market data processing
- **AI Inference**: ~5-10 seconds per decision (local models)
- **Memory Usage**: ~200-500MB typical operation
- **CPU Usage**: Optimized for continuous operation

## 🛡️ Risk Management

- **Adaptive Stops**: ATR-based stop-loss calculation
- **Position Sizing**: Dynamic position scaling
- **Market Regime**: Automatic leverage adjustment
- **Confidence Scoring**: AI confidence-based position sizing

## 📊 Supported Markets

- **Spot Trading**: DOGE/USDT, BTC/USDT, ETH/USDT
- **Futures Trading**: Leveraged positions with risk management
- **Exchange**: HTX (Huobi) via ccxt library
- **Timeframes**: 1m, 5m, 15m, 1h, 4h, 1d

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss. The authors are not responsible for any financial losses incurred through the use of this software. Always trade responsibly and never risk more than you can afford to lose.

## 📚 Documentation

Complete documentation is available in the [`docs/`](docs/) folder:

- **[Installation Guide](docs/guides/installation.md)** - Complete setup instructions
- **[GUI User Guide](docs/guides/gui-guide.md)** - Interface usage documentation
- **[API Reference](docs/api/)** - Developer API documentation
- **[Feature Documentation](docs/features/)** - Detailed feature guides

## 🆘 Support

- **Issues**: GitHub Issues
- **Documentation**: See [`docs/`](docs/) for comprehensive guides
- **Configuration**: Check `config/` directory for examples

---

**Built with ❤️ for the crypto trading community**
